"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { motion, Variants } from "motion/react";
import hands from "@/assets/Group 47385.png";
import thunder from "@/assets/thunder.png";
import trophy from "@/assets/trophy.png";
import hand from "@/assets/09d5333d8e054a96f2830dcb78b5e03f.png";
import { ChevronDown, Search, ArrowLeft, ArrowRight } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import CampaignCard from "../ui/CampaignCard";
const fadeIn = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.5 } },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const slideUp = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { type: "spring", stiffness: 300, damping: 24 },
  },
};

export default function LandingPage() {
  const [activePage, setActivePage] = useState(1);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div className="min-h-screen text-foreground md:px-8">
      {/* Hero Section */}

      <div className="relative mt-20">
        {/* <div className="absolute bottom-0 left-[-20%] right-0 top-[-10%] h-[500px] w-[500px] rounded-full bg-[radial-gradient(circle_farthest-side,rgba(136,212,53,.2),rgba(255,255,255,0))] blur-2xl"></div>
        <div className="absolute bottom-0 right-[-20%] top-[-10%] h-[500px] w-[500px] rounded-full bg-[radial-gradient(circle_farthest-side,rgba(136,212,53,.2),rgba(255,255,255,0))] blur-2xl"></div> */}

        {/* <div className="absolute bottom-0 left-1/2 -translate-x-1/2 top-[-160%]  md:h-[500px] w-[800px] rounded-full bg-[radial-gradient(circle_farthest-side,rgba(136,212,53,.2),rgba(255,255,255,0))] blur-3xl"></div> */}
        <motion.section
          initial="hidden"
          animate={isLoaded ? "visible" : "hidden"}
          variants={staggerContainer}
          className="flex flex-col items-center justify-center py-2 md:py-4 text-center max-w-[1460px] mx-auto"
        >
          <motion.h1
            variants={slideUp as Variants}
            className="text-3xl md:text-[42px] tracking-tight font-bold mb-6"
          >
            Launch a promotion campaign instantly!
          </motion.h1>

          <motion.div variants={slideUp as Variants}>
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-full flex items-center gap-2 transition-all duration-300 py-7 hover:scale-95 text-base scale-90 font-black z-10 relative">
              <Image
                src={hand}
                alt="hand"
                width={100}
                height={100}
                className="size-12 object-cover translate-x-1"
              />
              Promote Now
              <Image
                src={hand}
                alt="hand"
                width={100}
                height={100}
                className="-scale-x-100 size-12 object-cover -translate-x-1"
              />
            </Button>
          </motion.div>
        </motion.section>
      </div>

      {/* Featured Campaigns */}
      <motion.section
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={fadeIn}
        className="px-4 py-4 mt-40 mb-24 scale-95"
      >
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8"
        >
          <motion.div
            variants={slideUp as Variants}
            className="hidden md:block"
          >
            <CampaignCard
              title="Doge Coin"
              image="https://image.lexica.art/full_webp/045deaff-56a5-45fd-8f4d-72372f59cade"
              highlighted={false}
            />
          </motion.div>
          <motion.div variants={slideUp as Variants} className="relative">
            <Image
              src={hands}
              alt=""
              width={250}
              height={150}
              className="absolute md:scale-95 scale-90 -top-[238px] md:-top-[244px] left-1/2 -translate-x-1/2"
            />
            <CampaignCard
              title="FireAnime"
              image="https://image.lexica.art/full_webp/24fd65c2-8b62-45f4-b1b5-22403fbcc83f"
              highlighted={true}
            />
          </motion.div>
          <motion.div
            variants={slideUp as Variants}
            className="hidden md:block"
          >
            <CampaignCard
              title="GalaxyMonkey"
              image="https://image.lexica.art/full_webp/105382c4-07c5-4e0d-bf28-1c6c5f582c9d"
              highlighted={false}
            />
          </motion.div>
        </motion.div>
      </motion.section>

      {/* Search and Filter */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
        className="px-6 py-4 mx-auto max-w-7xl flex flex-col md:flex-row w-full items-center gap-2 md:gap-4"
      >
        <div className="flex items-center gap-2 w-full md:w-fit">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="rounded-full w-full h-12 font-semibold text-base ">
                <span>Sort:</span>
                <span className="bg-black text-transparent bg-clip-text">
                  {" "}
                  ✨ Trending
                </span>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-popover border-border">
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground focus:bg-accent">
                Trending
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground focus:bg-accent">
                Newest
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground focus:bg-accent">
                Highest Rate
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="relative flex-1 w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-primary" />
          <Input
            type="text"
            placeholder="Search Campaigns"
            className="w-full bg-secondary border-border rounded-full py-4 h-12 pl-10 pr-4 text-sm focus-visible:ring-primary focus-visible:ring-offset-0 focus-visible:border-primary"
          />
        </div>
      </motion.section>

      {/* Live Campaigns */}
      <motion.section
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={fadeIn}
        className="px-6 lg:px-10 py-4 mx-auto"
      >
        <motion.h2
          variants={slideUp as Variants}
          className="text-xl justify-center mb-8 md:text-3xl text-center w-full font-bold flex items-center gap-2"
        >
          <Image src={thunder} alt="" width={20} height={20} />
          <span>LIVE CAMPAIGNS</span>
          <Image src={thunder} alt="" width={20} height={20} />
        </motion.h2>

        <motion.div
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-4"
        >
          {[1, 2, 3, 4].map((i) => (
            <motion.div key={i} variants={slideUp as Variants}>
              <CampaignCard
                title={`X Campaign`}
                image="https://www.figma.com/file/Hq1vv17qB4qRUiyhRKywc9/image/b29c9df4c406014ef316bc1fab005ed0604d6a2c"
                highlighted={false}
              />
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4"
        >
          {[5, 6, 7, 8].map((i) => (
            <motion.div key={i} variants={slideUp as Variants}>
              <CampaignCard
                title={`X Campaign`}
                image="https://www.figma.com/file/Hq1vv17qB4qRUiyhRKywc9/image/b29c9df4c406014ef316bc1fab005ed0604d6a2c"
                highlighted={false}
              />
            </motion.div>
          ))}
        </motion.div>
      </motion.section>

      {/* Pagination */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="flex justify-center py-10"
      >
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full bg-secondary border-border hover:bg-secondary/80"
            onClick={() => setActivePage(Math.max(1, activePage - 1))}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>

          {[1, 2, 3].map((page) => (
            <Button
              key={page}
              variant={page === activePage ? "default" : "outline"}
              size="icon"
              className={`h-8 w-8 rounded-full ${
                page === activePage
                  ? "bg-primary text-primary-foreground hover:bg-primary/90"
                  : "bg-secondary border-border hover:bg-secondary/80"
              }`}
              onClick={() => setActivePage(page)}
            >
              {page}
            </Button>
          ))}

          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full bg-secondary border-border hover:bg-secondary/80"
          >
            ...
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full bg-secondary border-border hover:bg-secondary/80"
          >
            99
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full bg-secondary border-border hover:bg-secondary/80"
            onClick={() => setActivePage(Math.min(99, activePage + 1))}
          >
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </motion.section>

      {/* Leaderboard */}
      <motion.section
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={fadeIn}
        className="px-4 sm:px-8 py-6 sm:py-8 my-8 sm:my-12"
      >
        <motion.h2
          variants={slideUp as Variants}
          className="text-lg sm:text-xl justify-center mb-6 sm:mb-10 md:text-3xl text-center w-full font-bold flex items-center gap-2"
        >
          <Image src={trophy} alt="" width={20} height={20} />
          <span>LEADERBOARDS</span>
          <Image src={trophy} alt="" width={20} height={20} />
        </motion.h2>

        <motion.div
          variants={slideUp as Variants}
          className="w-full max-w-6xl mx-auto sm:scale-105"
        >
          {/* Column headers */}
          <div className="flex items-center justify-between px-3 sm:px-6 mb-2 sm:mb-3 text-xs sm:text-sm font-semibold text-foreground">
            <div className="flex items-center gap-2 sm:gap-4">
              <span className="w-5 sm:w-10">RANK</span>
              <span className="pl-8 sm:pl-12">USER</span>
            </div>
            <div className="flex items-center gap-4 sm:gap-10">
              <span className="w-20 sm:w-32 text-center">EARNED</span>
              <span className="hidden sm:inline w-12 text-right">VIEWS</span>
            </div>
          </div>

          {[
            { id: 1, name: "Ayrrad", earned: "699", views: "2.2M" },
            { id: 2, name: "Jasper", earned: "693", views: "2.1M" },
            { id: 3, name: "Meet", earned: "685", views: "2.0M" },
            { id: 4, name: "Alex", earned: "645", views: "1.9M" },
            { id: 5, name: "Taylor", earned: "620", views: "1.8M" },
            { id: 6, name: "Jordan", earned: "599", views: "1.7M" },
            { id: 7, name: "Casey", earned: "587", views: "1.6M" },
            { id: 8, name: "Riley", earned: "573", views: "1.5M" },
            { id: 9, name: "Morgan", earned: "550", views: "1.4M" },
          ].map((user) => (
            <motion.div
              key={user.id}
              variants={slideUp as Variants}
              className={`flex items-center justify-between py-3 sm:py-4 px-3 sm:px-6 border mb-2 rounded-xl sm:rounded-3xl ${
                user.id === 1
                  ? "bg-yellow-700/10 border-yellow-400"
                  : user.id === 2
                  ? "bg-cyan-700/10 border-cyan-400"
                  : user.id === 3
                  ? "bg-red-700/10 border-red-400"
                  : "bg-secondary/5 border-secondary"
              }`}
            >
              <div className="flex items-center gap-3 sm:gap-14">
                <span
                  className={`font-bold text-sm sm:text-lg ${
                    user.id === 1
                      ? "text-yellow-400"
                      : user.id === 2
                      ? "text-cyan-400"
                      : user.id === 3
                      ? "text-red-400"
                      : "text-foreground"
                  }`}
                >
                  #{user.id}
                </span>
                <div className="flex items-center gap-2 sm:gap-4">
                  <Avatar className="h-6 w-6 sm:h-8 sm:w-8">
                    <AvatarImage
                      src={`https://xvatar.vercel.app/api/avatar/${user.name.toLowerCase()}.svg?rounded=40&size=80`}
                      alt={user.name}
                    />
                    <AvatarFallback
                      className={`${
                        user.id === 1
                          ? "bg-yellow-400"
                          : user.id === 2
                          ? "bg-cyan-400"
                          : user.id === 3
                          ? "bg-red-400"
                          : "bg-primary"
                      } text-primary-foreground text-[10px] sm:text-xs`}
                    >
                      {user.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <span
                    className={`font-bold text-sm sm:text-base md:text-xl ${
                      user.id === 1
                        ? "text-yellow-400"
                        : user.id === 2
                        ? "text-cyan-400"
                        : user.id === 3
                        ? "text-red-400"
                        : "text-foreground"
                    }`}
                  >
                    {user.name}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-4 sm:gap-10">
                <Badge className="bg-primary text-primary-foreground border-0 rounded-full px-3 sm:px-6 py-0.5 sm:py-1.5 flex items-center font-bold text-sm sm:text-base md:text-lg">
                  <span className="mr-1 sm:mr-2">💵</span>${user.earned}
                </Badge>
                <span
                  className={`hidden sm:inline font-bold text-base sm:text-lg ${
                    user.id === 1
                      ? "text-yellow-400"
                      : user.id === 2
                      ? "text-cyan-400"
                      : user.id === 3
                      ? "text-red-400"
                      : "text-foreground"
                  }`}
                >
                  {user.views}
                </span>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </motion.section>
    </div>
  );
}
