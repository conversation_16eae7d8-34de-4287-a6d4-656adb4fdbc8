"use client";

import { useState } from "react";
import { motion } from "motion/react";
import {
  Settings,
  Link,
  Wallet,
  CreditCard,
  HelpCircle,
  Users,
  CheckCircle2,
  Play,
  LogOut,
  Check,
  Globe,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";

const sidebarItems = [
  { id: "general", label: "General", icon: Setting<PERSON> },
  { id: "connected", label: "Connected Accounts", icon: Link },
  { id: "wallet", label: "Connect Wallet", icon: Wallet },
  { id: "payments", label: "Payments", icon: CreditCard },
  { id: "resolution", label: "Resolution Center", icon: HelpCircle },
  { id: "referral", label: "Referral", icon: Users },
  { id: "verify", label: "Verify Demographics", icon: CheckCircle2 },
];

const socialPlatforms = [
  { id: "discord", name: "Discord", icon: "/discord.svg", username: "@ayrrad" },
  {
    id: "telegram",
    name: "Telegram",
    icon: "/telegram.svg",
    username: "@ayrrad",
  },
  {
    id: "instagram1",
    name: "Instagram",
    icon: "/instagram.svg",
    username: "@ayrrad",
  },
  {
    id: "instagram2",
    name: "Instagram",
    icon: "/instagram.svg",
    username: "@ayrrad",
  },
  {
    id: "discord2",
    name: "Discord",
    icon: "/discord.svg",
    username: "@ayrrad",
  },
  {
    id: "telegram2",
    name: "Telegram",
    icon: "/telegram.svg",
    username: "@ayrrad",
  },
];

export default function SettingsPage() {
  const [activeSection, setActiveSection] = useState("verify");
  const [currentStep, setCurrentStep] = useState("upload"); // "upload" or "social"
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handlePlatformToggle = (platformId: string) => {
    setSelectedPlatforms((prev) =>
      prev.includes(platformId)
        ? prev.filter((id) => id !== platformId)
        : [...prev, platformId]
    );
  };

  const renderUploadStep = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
      className="max-w-4xl"
    >
      <div className="bg-card border border-border rounded-2xl p-8">
        <div className="flex items-center gap-3 mb-8">
          <Globe className="w-6 h-6 text-primary" />
          <h1 className="text-2xl font-semibold text-foreground">
            Verify Demographics
          </h1>
        </div>
        <div className="space-y-6 mb-8">
          <p className="text-foreground font-medium text-lg">
            To verify your demographics, follow the steps below:
          </p>
          <ul className="space-y-3 text-muted-foreground">
            <li className="flex items-start gap-3">
              <span className="text-primary mt-1 text-lg">•</span>
              <span>Enable Screen Recording on your device.</span>
            </li>
            <li className="flex items-start gap-3">
              <span className="text-primary mt-1 text-lg">•</span>
              <span>
                Open your social media profile, ensure your username is clearly
                visible.
              </span>
            </li>
            <li className="flex items-start gap-3">
              <span className="text-primary mt-1 text-lg">•</span>
              <span>Navigate to the Analytics/Insights section.</span>
            </li>
            <li className="flex items-start gap-3">
              <span className="text-primary mt-1 text-lg">•</span>
              <span>Scroll to Demographics or Audience Location.</span>
            </li>
            <li className="flex items-start gap-3">
              <span className="text-primary mt-1 text-lg">•</span>
              <span>
                Record the full process with all information clearly visible.
              </span>
            </li>
          </ul>
        </div>

        <div className="space-y-6">
          <h3 className="text-xl font-medium text-foreground">
            Upload screen Recording
          </h3>

          <div className="border-2 border-dashed border-primary/70 rounded-xl p-12 text-center bg-background">
            <div className="flex flex-col items-center space-y-6">
              <div className="w-20 h-20 bg-primary/20 rounded-full flex items-center justify-center">
                <Play className="w-10 h-10 text-primary" />
              </div>
              <div className="space-y-2">
                <p className="text-foreground font-medium text-lg">
                  Upload your video file
                </p>
                <p className="text-yellow-500">
                  Our team will review your demographics within 12 hours
                </p>
              </div>
              <Button
                className="bg-white"
                onClick={() => document.getElementById("file-input")?.click()}
              >
                Select File
              </Button>
              <input
                id="file-input"
                type="file"
                accept="video/*"
                className="hidden"
                aria-label="Upload video file"
                onChange={(e) =>
                  e.target.files?.[0] && handleFileSelect(e.target.files[0])
                }
              />
            </div>
          </div>

          {selectedFile && (
            <p className="text-sm text-muted-foreground text-center">
              Selected: {selectedFile.name}
            </p>
          )}
        </div>

        <div className="flex gap-4 mt-10">
          <Button
            className="bg-primary hover:bg-primary/90 text-primary-foreground font-semibold flex-1 h-12 text-base"
            onClick={() => setCurrentStep("social")}
          >
            Submit
          </Button>
          <Button
            variant="outline"
            className="bg-transparent border-border text-muted-foreground hover:bg-muted px-8 h-12"
          >
            Back
          </Button>
        </div>
      </div>
    </motion.div>
  );

  const renderSocialStep = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
      className="max-w-4xl"
    >
      <div className="flex items-center gap-3 mb-8">
        <CheckCircle2 className="w-6 h-6 text-primary" />
        <h1 className="text-2xl font-semibold text-foreground">
          Verify Demographics
        </h1>
      </div>

      <div className="bg-card border border-border rounded-2xl p-8">
        <div className="space-y-8">
          <div>
            <h2 className="text-2xl font-semibold text-foreground mb-3">
              Verify your follower Demographics
            </h2>
            <p className="text-muted-foreground text-lg">
              Select a Connected account to begin
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            {socialPlatforms.map((platform) => (
              <Button
                key={platform.id}
                variant="outline"
                className={`h-20 p-4 border-2 transition-all rounded-xl ${
                  selectedPlatforms.includes(platform.id)
                    ? "border-primary bg-primary/10 text-foreground"
                    : "border-border bg-card text-muted-foreground hover:bg-muted hover:border-border"
                }`}
                onClick={() => handlePlatformToggle(platform.id)}
              >
                <div className="flex items-center gap-4 w-full">
                  <div className="w-10 h-10 relative flex-shrink-0">
                    <Image
                      src={platform.icon}
                      alt={platform.name}
                      width={40}
                      height={40}
                      className="object-contain"
                    />
                  </div>
                  <div className="flex flex-col items-start flex-1">
                    <span className="font-semibold text-base">
                      {platform.name}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {platform.username}
                    </span>
                  </div>
                  {selectedPlatforms.includes(platform.id) && (
                    <Check className="w-6 h-6 text-primary flex-shrink-0" />
                  )}
                </div>
              </Button>
            ))}
          </div>

          <Button
            className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-semibold h-12 text-base mt-8"
            disabled={selectedPlatforms.length === 0}
          >
            Next
          </Button>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="bg-background text-foreground min-h-screen pb-40 ">
      <div className="flex mt-12 mx-auto max-w-7xl">
        {/* Sidebar */}
        <div className="flex flex-col">
          <h2 className="text-foreground font-medium mb-6 px-3">
            Account Settings
          </h2>
          <div className="w-64 bg-card border-r border-border p-4 rounded-2xl overflow-hidden">
            <div className="space-y-1 mb-8">
              {sidebarItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeSection === item.id;

                return (
                  <Button
                    key={item.id}
                    variant="ghost"
                    className={`w-full justify-start gap-3 h-11 px-3 rounded-lg text-sm ${
                      isActive
                        ? "bg-primary text-primary-foreground font-medium"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted"
                    }`}
                    onClick={() => setActiveSection(item.id)}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{item.label}</span>
                  </Button>
                );
              })}
            </div>

            <div className="mt-60">
              <Button
                variant="ghost"
                className="w-full gap-3 py-2 bg-primary/10 border border-primary/60 rounded-full text-center px-3 text-primary justify-center"
              >
                <LogOut className="w-4 h-4" />
                <span className="text-sm">Log Out</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8 bg-background">
          {activeSection === "verify" &&
            (currentStep === "upload"
              ? renderUploadStep()
              : renderSocialStep())}

          {activeSection !== "verify" && (
            <div className="flex items-center justify-center h-96">
              <p className="text-muted-foreground text-lg">
                {sidebarItems.find((item) => item.id === activeSection)?.label}{" "}
                - Coming Soon
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
