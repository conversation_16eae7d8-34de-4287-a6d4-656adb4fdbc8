"use client";

import { motion } from "motion/react";
import logo from "@/assets/logo.png";
import Image from "next/image";
import { Button } from "../ui/button";

const Footer = () => {
  return (
    <motion.footer
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      className="border-t border-border py-8 md:px-8 md:py-4 flex items-center justify-between"
    >
      <Image
        src={logo}
        alt="Logo"
        width={120}
        height={100}
        placeholder="blur"
        className="size-20  object-contain hover:scale-125 hover:-rotate-6 transition-all duration-300 hidden md:block"
      />
      <div className="text-sm text-muted-foreground text-center w-full">
        © {new Date().getFullYear()} PromoteFun. All rights reserved.
      </div>

      <div className="justify-center gap-2 hidden md:flex">
        <Button size="icon" variant={"ghost"} className="rounded-full p-2">
          <Image
            src={"/insta.svg"}
            alt="telegram"
            className=""
            width={24}
            height={24}
          />
        </Button>
        <Button size="icon" variant={"ghost"} className="rounded-full p-2.5 ">
          <Image src={"/X.svg"} alt="x" width={24} height={24} />
        </Button>
        <Button size="icon" variant={"ghost"} className="rounded-full p-1">
          <Image src={"/discord2.svg"} alt="discord" width={25} height={25} />
        </Button>
      </div>
    </motion.footer>
  );
};

export default Footer;
