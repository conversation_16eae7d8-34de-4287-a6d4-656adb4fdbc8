"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { ImagePlus, Pencil, Wallet } from "lucide-react";
import Image from "next/image";

interface ProfileCardProps {
  username: string;
  balance: number;
  avatarUrl?: string;
  coverUrl?: string;
}

export default function ProfileCard({
  username,
  balance,
  avatarUrl,
  coverUrl,
}: ProfileCardProps) {
  return (
    <Card className="w-full max-w-xl rounded-3xl overflow-hidden border shadow-xl pt-0">
      {/* Cover Photo */}
      <div className="relative w-full h-52">
        <Image
          width={500}
          height={400}
          src={coverUrl || "/default-cover.jpg"}
          alt="cover"
          className="w-full h-full object-cover opacity-90"
        />
        <Button
          size="sm"
          className="absolute bottom-4 right-4 px-3 py-1.5 h-auto rounded-full"
        >
          <ImagePlus className="w-3.5 h-3.5 mr-1" />
          Add Cover Photo
        </Button>
      </div>

      <CardContent className="pt-0 pb-6 -mt-20 flex flex-col items-center">
        {/* Avatar */}
        <Avatar className="size-32 border-4">
          <AvatarImage
            src={
              avatarUrl || "https://xvatar.vercel.app/api/avatar/" + username
            }
            className="object-cover"
          />
          <AvatarFallback className="text-xl bg-accent">
            {username.charAt(0)}
          </AvatarFallback>
        </Avatar>

        {/* Username */}
        <div className="flex items-center gap-2 mt-3 text-2xl font-semibold">
          {username}
          <Pencil className="w-4 h-4 text-primary" />
        </div>

        {/* Withdraw + Balance */}
        <div className="flex items-center gap-3 mt-4">
          <Button className="rounded-full font-semibold">Withdraw Funds</Button>
          <div className="flex items-center gap-1 text-base font-semibold">
            <Wallet className="w-4 h-4 text-primary" />$
            {balance.toLocaleString()}
          </div>
        </div>

        {/* Social Icons */}
        <div className="mt-8 w-full px-4">
          <p className="text-sm mb-2 text-center">Add Socials</p>
          <div className="flex justify-center gap-3 md:gap-4">
            <Button
              size="icon"
              className="rounded-full p-2 bg-cyan-500 hover:bg-cyan-600"
            >
              <Image
                src={"/telegram.svg"}
                alt="telegram"
                className=""
                width={24}
                height={24}
              />
            </Button>
            <Button
              size="icon"
              className="rounded-full p-2.5 bg-zinc-800 hover:bg-zinc-900"
            >
              <Image src={"/X.svg"} alt="x" width={24} height={24} />
            </Button>
            <Button
              size="icon"
              className="rounded-full p-1 bg-indigo-500 hover:bg-indigo-600"
            >
              <Image
                src={"/discord.svg"}
                alt="discord"
                width={25}
                height={25}
              />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
