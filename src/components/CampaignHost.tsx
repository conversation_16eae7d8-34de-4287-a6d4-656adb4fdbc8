"use client";

import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import Image from "next/image";

const campaigns = Array(6).fill({
  title: "Promote Fun",
  paid: "$10,000",
  progress: 80,
});

export default function CampaignHost() {
  return (
    <Card className="w-full pt-0 max-w-3xl mx-auto overflow-hidden rounded-3xl border shadow-lg">
      {/* Banner */}
      <div className="relative w-full h-48">
        <Image
          src="https://image.lexica.art/full_webp/44b883ca-fcfe-43b8-8ae4-cc93b16e151e"
          alt="cover"
          fill
          className="object-cover opacity-90"
        />
        <div className="absolute -bottom-18 left-1/2 -translate-x-1/2">
          <Avatar className="size-36 border-4 border-card">
            <AvatarImage
              src="https://image.lexica.art/full_webp/44b883ca-fcfe-43b8-8ae4-cc93b16e151e"
              alt="avatar"
              className="object-cover"
            />
            <AvatarFallback>PF</AvatarFallback>
          </Avatar>
        </div>
      </div>

      <CardContent className="pt-16 pb-8 text-center relative">
        <h2 className="text-xl md:text-2xl font-semibold">Promote.Fun</h2>
        <p className="text-sm text-foreground/70 mb-4">
          <span className="text-foreground text-base mx-2">223k</span>
          Followers
        </p>

        <Button
          size={"lg"}
          className="rounded-full text-base font-bold absolute top-0 -translate-y-2 right-6"
        >
          Follow
        </Button>

        {/* Social Icons */}
        <div className="flex -translate-y-2 absolute top-0 justify-center gap-3">
          <Button
            size="icon"
            className="bg-cyan-500 hover:bg-cyan-600 rounded-full p-2"
          >
            <Image src="/telegram.svg" alt="telegram" width={24} height={24} />
          </Button>
          <Button
            size="icon"
            className="bg-zinc-800 hover:bg-zinc-900 rounded-full p-2"
          >
            <Image src="/X.svg" alt="x" width={24} height={24} />
          </Button>
          <Button
            size="icon"
            className="bg-indigo-500 hover:bg-indigo-600 rounded-full p-2"
          >
            <Image src="/discord.svg" alt="discord" width={24} height={24} />
          </Button>
        </div>

        <Tabs defaultValue="active" className="mt-10 w-full">
          <TabsList className="flex gap-2 bg-transparent justify-center">
            <TabsTrigger
              value="active"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary border-0 border-primary rounded-none px-4 py-4 bg-none data-[state=active]:bg-none rounded-t-2xl"
            >
              Active Campaigns
            </TabsTrigger>
            <TabsTrigger
              value="finished"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary border-0 border-primary rounded-none px-4 py-4 bg-none data-[state=active]:bg-none rounded-t-2xl"
            >
              Finished Campaigns
            </TabsTrigger>
          </TabsList>

          {/* Active Campaigns Tab */}
          <TabsContent value="active" className="mt-6 space-y-4">
            <div className="grid gap-3">
              <div className="grid grid-cols-3 px-4 py-2 text-sm font-medium text-muted-foreground border-b dark:border-zinc-800">
                <div className="text-left">Campaign</div>
                <div className="text-left">Paid</div>
                <div className="text-left">Progress</div>
              </div>

              {campaigns.map((c, i) => (
                <div
                  key={i}
                  className="grid grid-cols-3 items-center px-4 py-3 rounded-xl bg-muted/30 border-b dark:border-zinc-800/40"
                >
                  <div className="flex flex-col text-left">
                    <p className="text-foreground font-medium">{c.title}</p>
                  </div>

                  <div className="text-left">
                    <p className="text-foreground font-medium">{c.paid}</p>
                  </div>

                  <div className="w-full">
                    <div className="w-full h-2 bg-zinc-300 dark:bg-zinc-600 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-primary"
                        style={{ width: `${c.progress}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          {/* Finished Campaigns Tab */}
          <TabsContent
            value="finished"
            className="mt-6 text-center text-muted-foreground"
          >
            <p>No finished campaigns yet.</p>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
