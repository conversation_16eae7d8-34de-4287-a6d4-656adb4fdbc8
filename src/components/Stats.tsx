import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Chart<PERSON>ontainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Area, AreaChart, CartesianGrid } from "recharts";
import { <PERSON><PERSON>, Send, Twitter } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function CampaignStats() {
  const chartData = [
    { date: "Jan", views: 20, desktop: 15, mobile: 5 },
    { date: "Feb", views: 40, desktop: 30, mobile: 10 },
    { date: "Mar", views: 35, desktop: 25, mobile: 10 },
    { date: "Apr", views: 50, desktop: 35, mobile: 15 },
    { date: "May", views: 30, desktop: 20, mobile: 10 },
    { date: "Jun", views: 45, desktop: 30, mobile: 15 },
    { date: "Jul", views: 80, desktop: 55, mobile: 25 },
  ];

  return (
    <div className="bg-card rounded-xl p-6 space-y-8 w-full shadow-lg border-2 border-secondary/50">
      <div>
        <h2 className="text-xl font-bold mb-6">Campaign Statistics</h2>
        <div className="relative h-64">
          <ChartContainer
            config={{
              desktop: {
                label: "Desktop",
                color: "oklch(0.85 0.2156 132.08)",
              },
            }}
            className="h-52 w-full"
          >
            <AreaChart
              data={chartData}
              margin={{ top: 5, right: 5, bottom: 5, left: 5 }}
            >
              <defs>
                <linearGradient
                  id="desktopGradient"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop
                    offset="5%"
                    stopColor="oklch(0.85 0.2156 132.08)"
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor="oklch(0.85 0.2156 132.08)"
                    stopOpacity={0.1}
                  />
                </linearGradient>
                <linearGradient id="mobileGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor="oklch(0.75 0.15 132.08)"
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor="oklch(0.75 0.15 132.08)"
                    stopOpacity={0.1}
                  />
                </linearGradient>
              </defs>
              <CartesianGrid
                strokeDasharray="2 2"
                horizontal={true}
                vertical={false}
                stroke="currentColor"
                opacity={0.1}
              />
              {/* <XAxis
                dataKey="date"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickFormatter={(value) => value.slice(0, 3)}
              /> */}
              <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
              <Area
                type="monotone"
                dataKey="mobile"
                stackId="1"
                stroke="oklch(0.75 0.15 132.08)"
                strokeWidth={2}
                fillOpacity={0.4}
                fill="url(#mobileGradient)"
              />
            </AreaChart>
          </ChartContainer>
          <div className="absolute bottom-0 left-0">
            <div className="text-xl font-bold">80,000 Views</div>
          </div>
          <div className="absolute bottom-0 right-0">
            <Button
              variant="link"
              className="text-muted-foreground hover:text-primary transition-colors p-0"
            >
              Watch Video
            </Button>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="text-muted-foreground">Pool</div>
          <div className="text-xl font-bold text-primary">$10,000</div>
        </div>

        <div className="relative">
          <div className="h-2 bg-secondary/70 rounded-full">
            <div className="h-2 bg-primary rounded-full w-[45%] shadow-[0_0_10px_rgba(133,255,0,0.7)]"></div>
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <div className="flex text-sm justify-between items-center mb-4">
            <div className={"font-semibold text-muted-foreground"}>
              Rate per 1000 Views
            </div>{" "}
            $800
          </div>
          <div
            className={"h-px -translate-y-2 w-full bg-muted-foreground/40"}
          />
          <div className="flex text-sm justify-between items-center mb-6">
            <div className={"font-semibold text-muted-foreground"}>
              Accepted Platforms
            </div>
            <div className="flex gap-1.5">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Twitter className="p-1 bg-blue-500 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Twitter</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Glasses className="p-1 bg-indigo-500 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Discord</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Send className="p-1 bg-cyan-400 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Telegram</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </div>
      <div className="w-full flex items-center">
        <Button className="rounded-full mx-auto">Join Campaign</Button>
      </div>
    </div>
  );
}
