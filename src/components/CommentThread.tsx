import { <PERSON><PERSON> } from "@/components/ui/button";
import { Bold, Italic, Strikethrough, Link, Undo2 } from "lucide-react";
import { Textarea } from "./ui/textarea";

export default function CommentThread() {
  return (
    <div className="space-y-6 mt-8">
      <h2 className="text-lg font-medium">Thread</h2>

      {/* Comment Input */}
      <div className="dark:bg-[#1a1f2a] rounded-xl p-4 bg-gray-100">
        <Textarea
          placeholder="Type your comment here"
          className="bg-transparent border-none resize-none focus-visible:ring-0 focus-visible:ring-offset-0 h-20 placeholder:text-gray-500 shadow-none"
        />
        <div className="flex justify-between items-center mt-2">
          <div className="flex space-x-2 text-gray-400">
            <button>
              <Bold className="w-4 h-4" />
            </button>
            <button>
              <Italic className="w-4 h-4" />
            </button>
            <button>
              <Strikethrough className="w-4 h-4" />
            </button>
            <button>
              <Link className="w-4 h-4" />
            </button>
          </div>
          <Button className="bg-white text-black hover:bg-gray-200 rounded-full dark:invert-0 invert">
            Submit
          </Button>
        </div>
      </div>

      {/* Comments */}
      {[1, 2, 3].map((index) => (
        <div key={index} className="space-y-2">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 rounded-full bg-blue-500"></div>
            <span className="font-medium">JackSparow</span>
            <span className="text-gray-500 dark:text-gray-400 text-sm">35 mins ago</span>
          </div>
          <p className="text-muted-foreground">
            Lorem ipsum dolor sit amet consectetur. Risus sagittis nibh ornare
            consequat. Et suspendisse lectus iaculis mauris natoque justo
            faucibus.
          </p>
          <button className="flex items-center text-gray-400 text-sm">
            <Undo2 className="w-4 h-4 mr-1" />
            Reply
          </button>
          <div className="border-b dark:border-gray-800 pt-2"></div>
        </div>
      ))}
    </div>
  );
}
