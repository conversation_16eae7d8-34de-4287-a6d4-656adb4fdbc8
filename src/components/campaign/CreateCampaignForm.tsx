"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  BarChart3,
  Target,
  DollarSign,
  Send,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { cn } from "@/lib/utils";
import StepOne from "./steps/StepOne";
import StepTwo from "./steps/StepTwo";
import StepThree from "./steps/StepThree";
import StepFour from "./steps/StepFour";
import CampaignPreview from "./CampaignPreview";

interface FormData {
  // Step 1
  campaignName: string;
  description: string;
  links: {
    twitter: string;
    instagram: string;
    website: string;
  };

  // Step 2
  campaignType: string;
  geoRestrictions: string;
  pageTypes: string[];
  targetAudience: string;
  hashtags: string;
  minFollowers: number;
  minViews: number;
  maxPosts: number;
  maxPayout: number;
  requirements: string;

  // Step 3 & 4
  title: string;
  budget: number;
  costPerView: number;
  socialAccounts: {
    instagram: boolean;
    twitter: boolean;
    youtube: boolean;
    tiktok: boolean;
  };
  coverImage: File | null;
}

const steps = [
  {
    id: 1,
    title: "Name your Campaign",
    icon: BarChart3,
    description: "Basic campaign information",
  },
  {
    id: 2,
    title: "Campaign Settings",
    icon: Target,
    description: "Configure your campaign",
  },
  {
    id: 3,
    title: "Payment Overview",
    icon: DollarSign,
    description: "Review budget and costs",
  },
  {
    id: 4,
    title: "Launch Campaign",
    icon: Send,
    description: "Final setup and launch",
  },
];

export default function CreateCampaignForm() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    campaignName: "",
    description: "",
    links: {
      twitter: "",
      instagram: "",
      website: "",
    },
    campaignType: "General",
    geoRestrictions: "Top 5 Countries",
    pageTypes: [],
    targetAudience: "Top 5 Countries",
    hashtags: "",
    minFollowers: 50000,
    minViews: 750000,
    maxPosts: 0,
    maxPayout: 30,
    requirements: "",
    title: "",
    budget: 5900,
    costPerView: 0,
    socialAccounts: {
      instagram: true,
      twitter: true,
      youtube: true,
      tiktok: false,
    },
    coverImage: null,
  });

  const updateFormData = (data: Partial<FormData>) => {
    setFormData((prev) => ({ ...prev, ...data }));
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <StepOne formData={formData} updateFormData={updateFormData} />;
      case 2:
        return <StepTwo formData={formData} updateFormData={updateFormData} />;
      case 3:
        return (
          <StepThree formData={formData} updateFormData={updateFormData} />
        );
      case 4:
        return <StepFour formData={formData} updateFormData={updateFormData} />;
      default:
        return <StepOne formData={formData} updateFormData={updateFormData} />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Step Indicator */}
      <div className="border-b border max-w-7xl mx-auto rounded-2xl mt-10">
        <div className="mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className="flex items-center">
                  <div
                    className={cn(
                      "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors",
                      currentStep >= step.id
                        ? "bg-primary border-primary text-primary-foreground"
                        : "border-border text-muted-foreground"
                    )}
                  >
                    <step.icon className="w-5 h-5" />
                  </div>
                  <div className="ml-3 hidden md:block">
                    <p
                      className={cn(
                        "text-sm font-medium",
                        currentStep >= step.id
                          ? "text-foreground"
                          : "text-muted-foreground"
                      )}
                    >
                      {step.title}
                    </p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className="hidden md:block w-16 h-0.5 bg-border mx-6 rotate-90 translate-x-10" />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Form */}
          <div className="space-y-6">
            <Card className="p-6 bg-background">
              {renderStep()}

              {/* Navigation Buttons */}
              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={prevStep}
                  disabled={currentStep === 1}
                  className="flex items-center gap-2"
                >
                  <ChevronLeft className="w-4 h-4" />
                  Back
                </Button>

                <Button
                  onClick={nextStep}
                  disabled={currentStep === steps.length}
                  className="flex items-center gap-2 bg-primary hover:bg-primary/90"
                >
                  {currentStep === steps.length ? "Launch Campaign" : "Next"}
                  {currentStep < steps.length && (
                    <ChevronRight className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </Card>
          </div>

          {/* Right Panel - Preview */}
          <div>
            <CampaignPreview formData={formData} currentStep={currentStep} />
          </div>
        </div>
      </div>
    </div>
  );
}
