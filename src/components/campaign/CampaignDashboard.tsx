"use client";

import { motion } from "motion/react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import {
  DollarSign,
  Target,
  Eye,
  Calendar,
  Users,
  BarChart3,
  Copy,
  Heart,
  MessageCircle,
  Share2, MoveUpRight
} from "lucide-react";
import { useState } from "react";
import Image from "next/image";

const campaignData = {
  title: "Joyner Lucas Audio Campaign",
  shareUrl: "https://promote.fun/Campaign/Joycer",
  stats: {
    budget: "$10,000",
    tierCpm: "$300",
    guaranteedViews: "3 Million",
    duration: "July 17, 2025 - July 20, 2025",
    totalEarned: "$600,000",
    earned: "$0.05",
  },
  overview: {
    totalPosts: 10,
    totalCreators: 14,
    avgViewsPerPost: "45,000",
  },
  topCreators: [
    { rank: 1, username: "@<PERSON>", views: "2.2M", color: "text-yellow-400" },
    { rank: 2, username: "@<PERSON>", views: "2.2M", color: "text-cyan-400" },
    { rank: 3, username: "@Meet", views: "2.2M", color: "text-red-400" },
    { rank: 4, username: "@Mike", views: "2.2M", color: "text-gray-400" },
  ],
  liveStats: {
    views: "45.3K",
    likes: "74K",
    shares: "10K",
    comments: "5K",
  },
};

const chartData = [
  { day: "Mon", views: 20 },
  { day: "Tue", views: 35 },
  { day: "Wed", views: 45 },
  { day: "Thu", views: 60 },
  { day: "Fri", views: 55 },
  { day: "Sat", views: 70 },
  { day: "Sun", views: 80 },
];

const chartConfig = {
  views: {
    label: "Views",
    color: "oklch(0.85 0.2156 132.08)",
  },
};

export default function CampaignDashboard() {
  const [copied, setCopied] = useState(false);
  const campaign = campaignData;

  const handleCopyUrl = async () => {
    await navigator.clipboard.writeText(campaign.shareUrl);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-background"
    >
      {/* Main Content */}
      <main className="py-8 px-6 md:px-8 max-w-7xl mx-auto w-full">
        {/* Campaign Header */}
        <div className="border mt-10 p-4 rounded-xl mb-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl md:text-3xl font-bold text-foreground">
                {campaign.title}
              </h1>
              <MoveUpRight className="w-6 h-6 text-primary" />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Share dashboard
              </span>
              <div className="flex items-center gap-2 bg-card border border-primary rounded-lg px-3 py-2">
                <span className="text-sm font-mono text-muted-foreground">
                  {campaign.shareUrl}
                </span>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleCopyUrl}
                  className="h-6 w-6 p-0"
                >
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
              {copied && <span className="text-sm text-primary">Copied!</span>}
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <StatsCard
              icon={<DollarSign className="size-5 md:size-7" />}
              title="Budget"
              value={campaign.stats.budget}
            />
            <StatsCard
              icon={<Target className="size-5 md:size-7" />}
              title="Tier CPM"
              value={campaign.stats.tierCpm}
            />
            <StatsCard
              icon={<Eye className="size-5 md:size-7" />}
              title="Guaranteed Views"
              value={campaign.stats.guaranteedViews}
            />
            <StatsCard
              icon={<Calendar className="size-5 md:size-7" />}
              title="Duration"
              value={campaign.stats.duration}
            />
            <StatsCard
              icon={<DollarSign className="size-5 md:size-7" />}
              title="Total Earned"
              value={campaign.stats.totalEarned}
            />
            <StatsCard
              icon={<DollarSign className="size-5 md:size-7" />}
              title="Earned"
              value={campaign.stats.earned}
            />
          </div>
        </div>

        <div className="border rounded-xl mb-8 p-4">
          {/* Campaign Overview */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4 text-foreground">
              Campaign Overview
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <OverviewCard
                icon={<BarChart3 className="size-5 md:size-7" />}
                title="Total Posts"
                value={campaign.overview.totalPosts.toString()}
              />
              <OverviewCard
                icon={<Users className="size-5 md:size-7" />}
                title="Total Unique Creators"
                value={campaign.overview.totalCreators.toString()}
              />
              <OverviewCard
                icon={<Eye className="size-5 md:size-7" />}
                title="Average Views Per Post"
                value={campaign.overview.avgViewsPerPost}
              />
            </div>
          </div>

          {/* Top Creators and Live Stats */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Top Creators */}
            <Card className="bg-background">
              <CardContent className="pb-0">
                <h3 className="text-lg font-semibold mb-4 text-foreground">
                  Top Creators
                </h3>
                <div className="space-y-3">
                  {campaign.topCreators.map((creator) => (
                    <TopCreatorItem key={creator.rank} creator={creator} />
                  ))}
                </div>
              </CardContent>
              <Button variant="link" className="text-primary p-0 mx-auto">
                Full List
              </Button>
            </Card>

            {/* Live Stats */}
            <Card className="bg-background py-2">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-foreground">
                    Live Stats
                  </h3>
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 rounded-full bg-primary"></div>
                      <span className="text-muted-foreground">Views</span>
                      <span className="text-primary font-semibold">
                        {campaign.liveStats.views}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      <span className="text-muted-foreground">Likes</span>
                      <span className="text-green-500 font-semibold">
                        {campaign.liveStats.likes}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                      <span className="text-muted-foreground">Shares</span>
                      <span className="text-blue-500 font-semibold">
                        {campaign.liveStats.shares}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                      <span className="text-muted-foreground">Comments</span>
                      <span className="text-purple-500 font-semibold">
                        {campaign.liveStats.comments}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="h-64">
                  <ChartContainer config={chartConfig}>
                    <AreaChart data={chartData}>
                      <defs>
                        <linearGradient
                          id="viewsGradient"
                          x1="0"
                          y1="0"
                          x2="0"
                          y2="1"
                        >
                          <stop
                            offset="5%"
                            stopColor="oklch(0.85 0.2156 132.08)"
                            stopOpacity={0.8}
                          />
                          <stop
                            offset="95%"
                            stopColor="oklch(0.85 0.2156 132.08)"
                            stopOpacity={0.1}
                          />
                        </linearGradient>
                      </defs>
                      <CartesianGrid
                        strokeDasharray="3 3"
                        stroke="oklch(0.26 0.0158 252.4)"
                      />
                      <XAxis
                        dataKey="day"
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: "oklch(0.6 0.01 285.885)", fontSize: 12 }}
                      />
                      <YAxis hide />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Area
                        type="monotone"
                        dataKey="views"
                        stroke="oklch(0.85 0.2156 132.08)"
                        strokeWidth={2}
                        fill="url(#viewsGradient)"
                      />
                    </AreaChart>
                  </ChartContainer>
                </div>
              </CardContent>
              <Button className="w-fit mx-auto mb-4">Export Data</Button>
            </Card>
          </div>
        </div>

        {/* Posts Section */}
        <div className="mb-8 border rounded-xl p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-foreground">Posts</h3>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Sort:</span>
              <Button size="sm">Most Views</Button>
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <PostCard key={index} />
            ))}
          </div>
        </div>
      </main>
    </motion.div>
  );
}

// Component definitions
interface StatsCardProps {
  icon: React.ReactNode;
  title: string;
  value: string;
}

function StatsCard({ icon, title, value }: StatsCardProps) {
  return (
    <Card className="hover:border-primary/50 transition-colors py-1.5 md:py-2">
      <CardContent className="flex gap-1 md:gap-3 items-center">
        <div className="text-primary">{icon}</div>
        <div className="flex flex-col gap-0.5">
          <span className="text-sm text-muted-foreground">{title}</span>
          <div className="text-lg font-semibold text-foreground">{value}</div>
        </div>
      </CardContent>
    </Card>
  );
}

interface OverviewCardProps {
  icon: React.ReactNode;
  title: string;
  value: string;
}

function OverviewCard({ icon, title, value }: OverviewCardProps) {
  return (
    <Card className="hover:border-primary/50 transition-colors py-1.5 md:py-2">
      <CardContent className="flex gap-1 md:gap-3 items-center">
        <div className="text-primary">{icon}</div>
        <div className="flex flex-col gap-0.5">
          <span className="text-sm text-muted-foreground">{title}</span>
          <div className="text-2xl font-bold text-foreground">{value}</div>
        </div>
      </CardContent>
    </Card>
  );
}

interface Creator {
  rank: number;
  username: string;
  views: string;
  color: string;
}

interface TopCreatorItemProps {
  creator: Creator;
}

function TopCreatorItem({ creator }: TopCreatorItemProps) {
  const getRankBgColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "bg-yellow-500/10 border-yellow-500/50 hover:bg-yellow-500/30 text-yellow-500";
      case 2:
        return " bg-cyan-500/10 border-cyan-500/50 hover:bg-cyan-500/30 text-cyan-500";
      case 3:
        return " bg-red-500/10 border-red-500/50 hover:bg-red-500/30 text-red-500";
      default:
        return "bg-muted-foreground/20 border-muted-foreground/50 hover:bg-muted-foreground/30 text-foreground";
    }
  };

  return (
    <div
      className={
        "flex items-center justify-between p-3 rounded-full border-2 " +
        getRankBgColor(creator.rank)
      }
    >
      <div className="flex items-center gap-3">
        <span className="font-bold text-lg">#{creator.rank}</span>
        <Avatar className="w-8 h-8">
          <AvatarImage
            src={`https://xvatar.vercel.app/api/avatar/${creator.username
              .slice(1)
              .toLowerCase()}.svg?rounded=40&size=80`}
            alt={creator.username}
          />
          <AvatarFallback className="bg-primary text-primary-foreground text-xs">
            {creator.username.slice(1, 3).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <span className="font-bold text-lg md:text-xl">{creator.username}</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="text-sm">Total Views</span>
        <span className="font-bold">{creator.views}</span>
      </div>
    </div>
  );
}

function PostCard() {
  return (
    <Card className="bg-card border-border overflow-hidden hover:border-primary/50 transition-colors relative h-[400px]">
      <Image
        src={
          "https://assets.lummi.ai/assets/QmWYsRWRiQBTMJdb8y7qNgJMGMyVkCfH7KBKfbfaqHNoqZ?auto=format&w=1500"
        }
        alt="Post"
        width={600}
        height={800}
        className="object-cover absolute inset-0 size-full"
      />
      <div className="absolute w-full bottom-0 h-1/2 bg-gradient-to-b from-transparent to-black"></div>

      <div className="absolute bottom-0 z-20 p-4">
        <p className="text-white font-bold text-lg mb-2">@Ayyrad</p>
        <div className="flex items-center justify-between text-sm text-white font-bold mb-1">
          <div className="flex items-center gap-1">
            <Eye className="w-4 text-primary" />
            <span>5,458,432 Views</span>
          </div>
        </div>
        <div className="flex items-center justify-between text-sm text-white font-bold mb-1">
          <div className="flex items-center gap-1">
            <Heart className="w-4 text-primary" />
            <span>784,845 Likes</span>
          </div>
        </div>
        <div className="flex items-center justify-between text-sm text-white font-bold mb-1">
          <div className="flex items-center gap-1">
            <MessageCircle className="w-4 text-primary" />
            <span>100,424 Comments</span>
          </div>
        </div>
        <div className="flex items-center justify-between text-sm text-white font-bold">
          <div className="flex items-center gap-1">
            <Share2 className="w-4 text-primary" />
            <span>56,843 Shares</span>
          </div>
        </div>
      </div>
    </Card>
  );
}
