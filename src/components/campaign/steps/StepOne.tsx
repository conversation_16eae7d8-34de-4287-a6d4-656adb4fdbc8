"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { cn } from "@/lib/utils";
import {
  Image as ImageIcon,
  Instagram,
  Music2Icon,
  Youtube,
} from "lucide-react";

interface FormData {
  title: string;
  budget: number;
  socialAccounts: {
    instagram: boolean;
    twitter: boolean;
    youtube: boolean;
    tiktok: boolean;
  };
  coverImage: File | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

interface StepOneProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
}

export default function StepOne({ formData, updateFormData }: StepOneProps) {
  const [dragActive, setDragActive] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleInputChange = (field: string, value: any) => {
    updateFormData({ [field]: value });
  };

  const handleSocialToggle = (platform: string) => {
    updateFormData({
      socialAccounts: {
        ...formData.socialAccounts,
        [platform]:
          !formData.socialAccounts[
            platform as keyof typeof formData.socialAccounts
          ],
      },
    });
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      updateFormData({ coverImage: e.dataTransfer.files[0] });
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      updateFormData({ coverImage: e.target.files[0] });
    }
  };

  return (
    <div className="space-y-6">
      {/* Campaign Title */}
      <div className="space-y-2">
        <Label className="text-base font-medium">Campaign Title</Label>
        <Input
          placeholder="PromoteFun"
          value={formData.title}
          onChange={(e) => handleInputChange("title", e.target.value)}
          className="bg-background dark:bg-input/30"
        />
      </div>

      {/* rate Per 1000 Views */}
      <div className="space-y-2">
        <Label className="text-base font-medium">Rate Per 1000 Views</Label>
        <Input
          placeholder="$0"
          value={`$${formData.costPerView || 0}`}
          className="bg-background dark:bg-input/30"
          readOnly
        />
      </div>

      {/* Campaign Budget */}
      <div className="space-y-4">
        <Label className="text-base font-medium">Campaign Budget</Label>
        <div className="text-center">
          <div className="text-3xl font-bold text-primary mb-2">
            ${formData.budget.toLocaleString()}
          </div>
          <p className="text-sm text-muted-foreground mb-4">
            Estimated Views: 4.7M
          </p>
        </div>

        <div className="space-y-4">
          <div className="flex justify-between text-sm">
            <span>$500</span>
            <span>$100,000</span>
          </div>
          <Slider
            value={[formData.budget]}
            onValueChange={(value) => handleInputChange("budget", value[0])}
            max={10000}
            min={100}
            step={100}
            className="w-full"
          />
        </div>
      </div>

      {/* Select Social Accounts */}
      <div className="space-y-3">
        <Label className="text-base font-medium">Select Social Accounts</Label>
        <div className="flex gap-3">
          <Button
            type="button"
            className={cn(
              "flex items-center gap-2 rounded-full",
              formData.socialAccounts.instagram
                ? "bg-primary text-primary-foreground"
                : "bg-background dark:bg-input/30"
            )}
            onClick={() => handleSocialToggle("instagram")}
          >
            <Instagram className="w-4 h-4" />
          </Button>

          <Button
            type="button"
            className={cn(
              "flex items-center gap-2 rounded-full",
              formData.socialAccounts.twitter
                ? "bg-primary text-primary-foreground"
                : "bg-background dark:bg-input/30"
            )}
            onClick={() => handleSocialToggle("twitter")}
          >
            <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
              <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
            </svg>
          </Button>

          <Button
            type="button"
            className={cn(
              "flex items-center gap-2 rounded-full",
              formData.socialAccounts.youtube
                ? "bg-primary text-primary-foreground"
                : "bg-background dark:bg-input/30"
            )}
            onClick={() => handleSocialToggle("youtube")}
          >
            <Youtube className="w-4 h-4" />
          </Button>
          <Button
            type="button"
            className={cn(
              "flex items-center gap-2 rounded-full",
              formData.socialAccounts.instagram
                ? "bg-primary text-primary-foreground"
                : "bg-background dark:bg-input/30"
            )}
            onClick={() => handleSocialToggle("instagram")}
          >
            <Music2Icon className="w-4 h-4" strokeWidth={4} />
          </Button>
        </div>
      </div>

      {/* Upload Cover Image */}
      <div className="space-y-5">
        <Label className="text-base font-medium ">Upload Cover Image</Label>
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-8 text-center transition-colors relative overflow-hidden",
            dragActive ? "border-primary bg-primary/5" : "border-border",
            "hover:border-primary hover:bg-primary/5"
          )}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          {/*eslint-disable-next-line jsx-a11y/alt-text */}
          <ImageIcon className="w-66 h-66 mx-auto mb-4 text-muted-foreground/10 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20" />
          <p className="text-sm text-muted-foreground mb-2">Supported Files:</p>
          <p className="text-xs text-muted-foreground mb-4">
            .jpeg, .png, .webp
            <br />
            (&lt; 2MB)
          </p>
          <input
            type="file"
            accept=".jpeg,.jpg,.png,.webp"
            onChange={handleFileChange}
            className="hidden"
            id="cover-upload"
            aria-label="Upload cover image"
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => document.getElementById("cover-upload")?.click()}
          >
            Upload
          </Button>
          {formData.coverImage && (
            <p className="text-sm text-primary mt-2">
              {formData.coverImage.name}
            </p>
          )}
        </div>
      </div>

      {/* Continue Button */}
      <Button
        className="w-full bg-primary hover:bg-primary/90 text-primary-foreground py-3"
        size="lg"
      >
        Continue
      </Button>
    </div>
  );
}
