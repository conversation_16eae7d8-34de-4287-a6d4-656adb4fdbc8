"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { HelpCircle, Trash2, Plus } from "lucide-react";
import { useState } from "react";

interface FormData {
  campaignType: string;
  geoRestrictions: string;
  pageTypes: string[];
  targetAudience: string;
  hashtags: string;
  minFollowers: number;
  minViews: number;
  maxPosts: number;
  maxPayout: number;
  requirements: string;
  assetLink: string;
  videoRequirements: string[];
  audioRequirement: string;
  tiktokAudio: string;
  whoCanJoin: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

interface StepTwoProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
}

export default function StepTwo({ formData, updateFormData }: StepTwoProps) {
  const [videoRequirements, setVideoRequirements] = useState<string[]>([
    "Must tag @PromoteFun in the description",
    "Video must be longer than 15 seconds",
    "Must Include Watermark",
  ]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleInputChange = (field: string, value: any) => {
    updateFormData({ [field]: value });
  };

  const handlePageTypeToggle = (pageType: string) => {
    const currentTypes = formData.pageTypes || [];
    const newTypes = currentTypes.includes(pageType)
      ? currentTypes.filter((type) => type !== pageType)
      : [...currentTypes, pageType];
    updateFormData({ pageTypes: newTypes });
  };

  const removeVideoRequirement = (index: number) => {
    const newRequirements = videoRequirements.filter((_, i) => i !== index);
    setVideoRequirements(newRequirements);
  };

  const addVideoRequirement = () => {
    const requirement = prompt("Enter video requirement:");
    if (requirement && requirement.trim()) {
      setVideoRequirements([...videoRequirements, requirement.trim()]);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
        {/* Left Column - Launch A Campaign */}
        <div className="space-y-8">
          <h1 className="text-2xl font-semibold text-white mb-8">
            Launch A Campaign
          </h1>

          {/* Campaign Type */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Label className="text-lg font-medium text-white">
                Campaign Type
              </Label>
              <span className="text-gray-400 text-sm">(Required)</span>
            </div>
            <RadioGroup
              value={formData.campaignType || "Clipping"}
              onValueChange={(value) =>
                handleInputChange("campaignType", value)
              }
              className="space-y-3"
            >
              <div className="flex items-center space-x-3">
                <RadioGroupItem
                  value="Clipping"
                  id="clipping"
                  className="border-green-500 text-green-500"
                />
                <Label htmlFor="clipping" className="text-white">
                  Clipping
                </Label>
              </div>
              <div className="flex items-center space-x-3">
                <RadioGroupItem
                  value="User Generated Content"
                  id="ugc"
                  className="border-gray-400"
                />
                <Label htmlFor="ugc" className="text-white">
                  User Generated Content
                </Label>
              </div>
              <div className="flex items-center space-x-3">
                <RadioGroupItem
                  value="Other"
                  id="other-campaign"
                  className="border-gray-400"
                />
                <Label htmlFor="other-campaign" className="text-white">
                  Other
                </Label>
              </div>
            </RadioGroup>
            <Button
              variant="outline"
              className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600 px-6"
            >
              Specify
            </Button>
          </div>

          {/* Geographic Restrictions */}
          <div className="space-y-4">
            <Label className="text-lg font-medium text-white">
              Geographic Restrictions
            </Label>
            <RadioGroup
              value={formData.geoRestrictions || "Specific"}
              onValueChange={(value) =>
                handleInputChange("geoRestrictions", value)
              }
              className="space-y-3"
            >
              <div className="flex items-center space-x-3">
                <RadioGroupItem
                  value="Tier 1 countries only"
                  id="tier1"
                  className="border-gray-400"
                />
                <Label htmlFor="tier1" className="text-white">
                  Tier 1 countries only
                </Label>
              </div>
              <div className="flex items-center space-x-3">
                <RadioGroupItem
                  value="Specific"
                  id="specific"
                  className="border-green-500 text-green-500"
                />
                <Label htmlFor="specific" className="text-white">
                  Specific
                </Label>
              </div>
            </RadioGroup>
            <Button
              variant="outline"
              className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600 px-6"
            >
              Write your preferences
            </Button>
          </div>

          {/* Page Types */}
          <div className="space-y-4">
            <div>
              <Label className="text-lg font-medium text-white">
                What types of pages are allowed to Join this campaign?
              </Label>
              <p className="text-gray-400 text-sm mt-1">
                Select all that apply
              </p>
            </div>
            <div className="space-y-3">
              {[
                "Fan pages (artist pages, Celebrity highlights, lyric edits)",
                "Meme (trending humor, cultural memes, viral moments)",
                "Video Clipping (stream clips, YouTubers, sports highlights)",
                "Influencer's (personal brand , UGC Creator)",
                "Other",
              ].map((pageType) => (
                <div key={pageType} className="flex items-center space-x-3">
                  <Checkbox
                    id={pageType}
                    checked={
                      formData.pageTypes?.includes(pageType) ||
                      pageType ===
                        "Meme (trending humor, cultural memes, viral moments)"
                    }
                    onCheckedChange={() => handlePageTypeToggle(pageType)}
                    className="border-green-500 data-[state=checked]:bg-green-500"
                  />
                  <Label htmlFor={pageType} className="text-white text-sm">
                    {pageType}
                  </Label>
                </div>
              ))}
            </div>
            <Button
              variant="outline"
              className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600 px-6"
            >
              Describe other accepted page types
            </Button>
          </div>

          {/* Target Creator Audience Location */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Label className="text-lg font-medium text-white">
                Target Creator Audience Location
              </Label>
              <HelpCircle className="w-4 h-4 text-gray-400" />
            </div>
            <RadioGroup
              value={formData.targetAudience || "Tier 1 Countries"}
              onValueChange={(value) =>
                handleInputChange("targetAudience", value)
              }
              className="space-y-4"
            >
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <RadioGroupItem
                    value="Tier 1 Countries"
                    id="target-tier1"
                    className="border-green-500 text-green-500"
                  />
                  <Label
                    htmlFor="target-tier1"
                    className="text-white font-medium"
                  >
                    Tier 1 Countries
                  </Label>
                </div>
                <div className="ml-8">
                  <p className="text-green-400 text-sm">
                    Premium markets with strong buyer intent and high CPMs
                  </p>
                  <p className="text-gray-400 text-sm">
                    United States, Canada, United Kingdom, Australia, Germany,
                    New Zealand, Netherlands, Sweden
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <RadioGroupItem
                    value="Tier 2 Countries"
                    id="target-tier2"
                    className="border-gray-400"
                  />
                  <Label
                    htmlFor="target-tier2"
                    className="text-white font-medium"
                  >
                    Tier 2 Countries
                  </Label>
                </div>
                <div className="ml-8">
                  <p className="text-green-400 text-sm">
                    Mid-range markets with good engagement and moderate CPMs
                  </p>
                  <p className="text-gray-400 text-sm">
                    France, Italy, Spain, Brazil, Mexico, Poland, Japan, South
                    Korea
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <RadioGroupItem
                    value="Tier 3 Countries"
                    id="target-tier3"
                    className="border-gray-400"
                  />
                  <Label
                    htmlFor="target-tier3"
                    className="text-white font-medium"
                  >
                    Tier 3 Countries
                  </Label>
                </div>
                <div className="ml-8">
                  <p className="text-green-400 text-sm">
                    Broad reach markets with lower CPMs, often used for volume
                  </p>
                  <p className="text-gray-400 text-sm">
                    India, Indonesia, Philippines, Vietnam, Nigeria, Pakistan,
                    Egypt, Bangladesh
                  </p>
                </div>
              </div>
            </RadioGroup>
          </div>

          {/* Hashtags */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Label className="text-lg font-medium text-white">Hashtags</Label>
              <span className="text-gray-400 text-sm">(Optional)</span>
            </div>
            <Input
              placeholder="Write hashtags separate by comma"
              value={formData.hashtags || ""}
              onChange={(e) => handleInputChange("hashtags", e.target.value)}
              className="bg-gray-800 border-gray-600 text-white placeholder-gray-400"
            />
          </div>
        </div>

        {/* Right Column - Campaign Settings */}
        <div className="space-y-8">
          {/* Minimum Follower Count per page */}
          <div className="space-y-4">
            <Label className="text-lg font-medium text-white">
              Minimum Follower Count per page
            </Label>
            <RadioGroup
              value={formData.minFollowers?.toString() || "10000"}
              onValueChange={(value) =>
                handleInputChange("minFollowers", parseInt(value))
              }
              className="space-y-3"
            >
              <div className="flex items-center space-x-3">
                <RadioGroupItem
                  value="0"
                  id="no-limit"
                  className="border-gray-400"
                />
                <Label htmlFor="no-limit" className="text-white">
                  No Limit
                </Label>
              </div>
              <div className="flex items-center space-x-3">
                <RadioGroupItem
                  value="1000"
                  id="1k"
                  className="border-gray-400"
                />
                <Label htmlFor="1k" className="text-white">
                  1000+
                </Label>
              </div>
              <div className="flex items-center space-x-3">
                <RadioGroupItem
                  value="10000"
                  id="10k"
                  className="border-green-500 text-green-500"
                />
                <Label htmlFor="10k" className="text-white">
                  10000+
                </Label>
              </div>
              <div className="flex items-center space-x-3">
                <RadioGroupItem
                  value="100000"
                  id="100k"
                  className="border-gray-400"
                />
                <Label htmlFor="100k" className="text-white">
                  100000+
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Minimum Views for payout */}
          <div className="space-y-4">
            <Label className="text-lg font-medium text-white">
              Minimum Views for payout
            </Label>
            <div className="space-y-4">
              <Slider
                value={[formData.minViews || 800000]}
                onValueChange={(value) =>
                  handleInputChange("minViews", value[0])
                }
                max={2000000}
                min={0}
                step={50000}
                className="w-full [&_[role=slider]]:bg-green-500 [&_[role=slider]]:border-green-500 [&_.bg-primary]:bg-green-500"
              />
              <div className="flex justify-between text-sm text-gray-400">
                <span>No Minimum</span>
                <span>2 Million</span>
              </div>
            </div>
          </div>

          {/* Max Posts Per Page */}
          <div className="space-y-4">
            <Label className="text-lg font-medium text-white">
              Max Posts Per Page
            </Label>
            <div className="space-y-4">
              <Slider
                value={[formData.maxPosts || 0]}
                onValueChange={(value) =>
                  handleInputChange("maxPosts", value[0])
                }
                max={10}
                min={0}
                step={1}
                className="w-full [&_[role=slider]]:bg-green-500 [&_[role=slider]]:border-green-500 [&_.bg-primary]:bg-green-500"
              />
              <div className="flex justify-between text-sm text-gray-400">
                <span>0</span>
                <span>No Limit</span>
              </div>
            </div>
          </div>

          {/* Max Payout Per Creator */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Label className="text-lg font-medium text-white">
                Max Payout Per Creator
              </Label>
              <HelpCircle className="w-4 h-4 text-gray-400" />
            </div>
            <div className="space-y-4">
              <Slider
                value={[formData.maxPayout || 30]}
                onValueChange={(value) =>
                  handleInputChange("maxPayout", value[0])
                }
                max={100}
                min={0}
                step={5}
                className="w-full [&_[role=slider]]:bg-green-500 [&_[role=slider]]:border-green-500 [&_.bg-primary]:bg-green-500"
              />
              <div className="flex justify-between text-sm text-gray-400">
                <span>0%</span>
                <span>30%</span>
              </div>
            </div>
          </div>

          {/* Who can Join */}
          <div className="space-y-4">
            <Label className="text-lg font-medium text-white">
              Who can Join?
            </Label>
            <RadioGroup
              value={
                formData.whoCanJoin ||
                "Any page that matches your requirements can join!"
              }
              onValueChange={(value) => handleInputChange("whoCanJoin", value)}
              className="space-y-3"
            >
              <div className="flex items-center space-x-3">
                <RadioGroupItem
                  value="Creators apply, you approve/deny them"
                  id="creators-apply"
                  className="border-gray-400"
                />
                <Label htmlFor="creators-apply" className="text-white text-sm">
                  Creators apply, you approve/deny them
                </Label>
              </div>
              <div className="flex items-center space-x-3">
                <RadioGroupItem
                  value="Any page that matches your requirements can join!"
                  id="anyone-join"
                  className="border-green-500 text-green-500"
                />
                <Label htmlFor="anyone-join" className="text-white text-sm">
                  Any page that matches your requirements can join!
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Asset links */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Label className="text-lg font-medium text-white">
                Asset links
              </Label>
              <HelpCircle className="w-4 h-4 text-gray-400" />
            </div>
            <div className="space-y-2">
              <p className="text-gray-400 text-sm">Enter a Link</p>
              <Input
                placeholder="https://drive.google.com/drive/"
                value={formData.assetLink || ""}
                onChange={(e) => handleInputChange("assetLink", e.target.value)}
                className="bg-gray-800 border-gray-600 text-white placeholder-gray-400"
              />
            </div>
          </div>

          {/* Requirements */}
          <div className="space-y-4">
            <Label className="text-lg font-medium text-white">
              Requirements
            </Label>
            <p className="text-gray-400 text-sm">Enter any video requirement</p>

            <div className="space-y-3">
              {videoRequirements.map((requirement, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between bg-gray-800 border border-gray-600 px-4 py-3 rounded-md"
                >
                  <span className="text-white text-sm">{requirement}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeVideoRequirement(index)}
                    className="text-red-400 hover:text-red-300 p-1"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}

              <Button
                variant="outline"
                onClick={addVideoRequirement}
                className="w-full border-green-500 text-green-500 hover:bg-green-500 hover:text-white bg-transparent flex items-center justify-center"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Audio Requirement */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Label className="text-lg font-medium text-white">
                Audio Requirement
              </Label>
              <HelpCircle className="w-4 h-4 text-gray-400" />
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3 bg-gray-800 border border-gray-600 px-4 py-3 rounded-md">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                  </svg>
                </div>
                <Input
                  placeholder="https://www.instagram.com/reels/audio/1234556"
                  value={formData.audioRequirement || ""}
                  onChange={(e) =>
                    handleInputChange("audioRequirement", e.target.value)
                  }
                  className="flex-1 border-0 bg-transparent text-white placeholder-gray-400"
                />
              </div>

              <div className="flex items-center space-x-3 bg-gray-800 border border-gray-600 px-4 py-3 rounded-md">
                <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">T</span>
                </div>
                <Input
                  placeholder="https://www.tiktok.com/music/1234556"
                  value={formData.tiktokAudio || ""}
                  onChange={(e) =>
                    handleInputChange("tiktokAudio", e.target.value)
                  }
                  className="flex-1 border-0 bg-transparent text-white placeholder-gray-400"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
