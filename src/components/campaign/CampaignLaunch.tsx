"use client";

import { useState } from "react";
import { motion } from "motion/react";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { CloudUpload } from "lucide-react";
import { cn } from "@/lib/utils";

export default function CampaignLaunch() {
  const [platformSelection, setPlatformSelection] = useState<{
    twitter: boolean;
    instagram: boolean;
    youtube: boolean;
    tiktok: boolean;
  }>({
    twitter: true,
    instagram: true,
    youtube: true,
    tiktok: false,
  });

  const [poolAmount, setPoolAmount] = useState<string>("$0");
  const [payPerView, setPayPerView] = useState<string>("$0");

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex justify-center items-center py-10"
    >
      <Card className="w-full max-w-2xl rounded-3xl overflow-hidden border shadow-xl bg-card">
        <CardContent className="pt-6 pb-8 px-8">
          <h2 className="text-2xl md:text-3xl font-semibold mb-6">
            Campaign Details
          </h2>

          <div className="w-full space-y-6">
            {/* Campaign Name */}
            <div className="space-y-2">
              <label className="text-base font-medium">
                What do you want to name the campaign
              </label>
              <Input
                type="text"
                placeholder="PromoteFun"
                className="h-12 mt-1 rounded-md bg-background dark:bg-input/30"
              />
            </div>

            {/* Pool Amount */}
            <div className="space-y-2">
              <label className="text-base font-medium">
                Choose pool amount
              </label>
              <div className="relative">
                <Input
                  type="text"
                  value={poolAmount}
                  onChange={(e) => setPoolAmount(e.target.value)}
                  className="h-12 mt-1 rounded-md bg-background dark:bg-input/30 pr-10"
                />
              </div>
            </div>

            {/* Campaign Description */}
            <div className="space-y-2">
              <label className="text-base font-medium">
                Describe your campaign
              </label>
              <textarea
                placeholder="Describe your campaign..."
                className={cn(
                  "file:text-foreground mt-1 placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex w-full min-w-0 rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm min-h-[120px] resize-none",
                  "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
                  "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive"
                )}
              />
            </div>

            {/* Platform Selection */}
            <div className="space-y-2">
              <label className="text-base font-medium">
                Choose a platform on which you want to run the campaign
              </label>
              <div className="flex flex-wrap gap-3 mt-1">
                <Button
                  type="button"
                  variant={platformSelection.twitter ? "default" : "outline"}
                  className={cn(
                    "rounded-full",
                    platformSelection.twitter
                      ? "bg-primary text-primary-foreground"
                      : "bg-background dark:bg-input/30"
                  )}
                  onClick={() =>
                    setPlatformSelection({
                      ...platformSelection,
                      twitter: !platformSelection.twitter,
                    })
                  }
                >
                  <svg
                    className="size-4"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M22 4.01C21.9 4.3 21.8 4.59 21.7 4.87C21.1 6.15 20.3 7.29 19.3 8.22C19.3 8.47 19.3 8.72 19.3 8.97C19.3 14.69 15.1 21.23 7.1 21.23C4.9 21.23 2.9 20.57 1.2 19.44C1.5 19.48 1.8 19.5 2.1 19.5C3.9 19.5 5.5 18.85 6.8 17.81C5.1 17.78 3.7 16.67 3.2 15.12C3.4 15.16 3.7 15.19 4 15.19C4.3 15.19 4.7 15.13 5 15.03C3.2 14.65 1.9 13.03 1.9 11.1C1.9 11.09 1.9 11.08 1.9 11.07C2.4 11.35 3 11.5 3.6 11.53C2.6 10.83 1.9 9.7 1.9 8.38C1.9 7.66 2.1 7 2.4 6.44C4.3 8.83 7.2 10.4 10.4 10.58C10.3 10.31 10.3 10.02 10.3 9.75C10.3 7.57 12.1 5.77 14.3 5.77C15.4 5.77 16.4 6.22 17.1 6.97C18 6.79 18.8 6.46 19.6 6.01C19.3 6.95 18.7 7.72 17.9 8.22C18.7 8.14 19.5 7.91 20.2 7.56C19.6 8.35 18.9 9.04 18.1 9.62C18.1 9.75 18.1 9.89 18.1 10.02C18.1 15.74 13.9 22.28 5.9 22.28C3.7 22.28 1.7 21.62 0 20.49C0.3 20.53 0.6 20.55 0.9 20.55C2.7 20.55 4.3 19.9 5.6 18.86C3.9 18.83 2.5 17.72 2 16.17C2.2 16.21 2.5 16.24 2.8 16.24C3.1 16.24 3.5 16.18 3.8 16.08C2 15.7 0.7 14.08 0.7 12.15C0.7 12.14 0.7 12.13 0.7 12.12C1.2 12.4 1.8 12.55 2.4 12.58C1.4 11.88 0.7 10.75 0.7 9.43C0.7 8.71 0.9 8.05 1.2 7.49C3.1 9.88 6 11.45 9.2 11.63C9.1 11.36 9.1 11.07 9.1 10.8C9.1 8.62 10.9 6.82 13.1 6.82C14.2 6.82 15.2 7.27 15.9 8.02C16.8 7.84 17.6 7.51 18.4 7.06C18.1 8 17.5 8.77 16.7 9.27C17.5 9.19 18.3 8.96 19 8.61C18.4 9.4 17.7 10.09 16.9 10.67C16.9 10.8 16.9 10.94 16.9 11.07C16.9 16.79 12.7 23.33 4.7 23.33C2.5 23.33 0.5 22.67 -1.2 21.54"
                      fill="currentColor"
                    />
                  </svg>
                  X (Twitter)
                </Button>
                <Button
                  type="button"
                  variant={platformSelection.instagram ? "default" : "outline"}
                  className={cn(
                    "rounded-full",
                    platformSelection.instagram
                      ? "bg-primary text-primary-foreground"
                      : "bg-background dark:bg-input/30"
                  )}
                  onClick={() =>
                    setPlatformSelection({
                      ...platformSelection,
                      instagram: !platformSelection.instagram,
                    })
                  }
                >
                  <svg
                    className="size-4"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"
                      fill="currentColor"
                    />
                  </svg>
                  Instagram
                </Button>
                <Button
                  type="button"
                  variant={platformSelection.youtube ? "default" : "outline"}
                  className={cn(
                    "rounded-full",
                    platformSelection.youtube
                      ? "bg-primary text-primary-foreground"
                      : "bg-background dark:bg-input/30"
                  )}
                  onClick={() =>
                    setPlatformSelection({
                      ...platformSelection,
                      youtube: !platformSelection.youtube,
                    })
                  }
                >
                  <svg
                    className="size-4"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"
                      fill="currentColor"
                    />
                  </svg>
                  Youtube
                </Button>
                <Button
                  type="button"
                  variant={platformSelection.tiktok ? "default" : "outline"}
                  className={cn(
                    "rounded-full",
                    platformSelection.tiktok
                      ? "bg-primary text-primary-foreground"
                      : "bg-background dark:bg-input/30"
                  )}
                  onClick={() =>
                    setPlatformSelection({
                      ...platformSelection,
                      tiktok: !platformSelection.tiktok,
                    })
                  }
                >
                  <svg
                    className="size-4"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"
                      fill="currentColor"
                    />
                  </svg>
                  Tiktok
                </Button>
              </div>
            </div>

            {/* Pay Per View */}
            <div className="space-y-2">
              <label className="text-base font-medium">
                How much you want to pay per 1000 views?
              </label>
              <div className="relative">
                <Input
                  type="text"
                  value={payPerView}
                  onChange={(e) => setPayPerView(e.target.value)}
                  className="h-12 mt-1 rounded-md bg-background dark:bg-input/30 pr-10"
                />
              </div>
            </div>

            {/* Watermark */}
            <div className="space-y-2 flex flex-col">
              <label className="text-base font-medium">
                watermark to use (skip if not required)
              </label>
              <Button
                type="button"
                className="rounded-full w-fit has-[>svg]:px-6"
              >
                <CloudUpload className="size-5 mr-1" />
                Upload Image
              </Button>
            </div>

            {/* Content Description */}
            <div className="space-y-2">
              <label className="text-base font-medium">
                Describe the content and add relevant links
              </label>
              <textarea
                placeholder="Describe the content and add relevant links"
                className={cn(
                  "file:text-foreground mt-1 placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex w-full min-w-0 rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm min-h-[120px] resize-none",
                  "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
                  "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive"
                )}
              />
            </div>

            {/* Next Button */}
            <Button
              type="button"
              className="w-full h-12 rounded-full text-lg font-semibold mt-4"
            >
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
