"use client";

import Image from "next/image";
import { motion } from "motion/react";
import {
  Heart,
  MoreHorizontal,
  Flame,
  Twitter,
  Send,
  Glasses,
} from "lucide-react";
import fire1 from "@/assets/fire1.png";
import fire2 from "@/assets/fire2.png";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

export default function CampaignCard({
  title,
  image,
  highlighted,
}: {
  title: string;
  image: string;
  highlighted: boolean;
}) {
  return (
    <motion.div className="group">
      <Card
        className={cn(
          "overflow-hidden gap-2 border-0 shadow-lg transition-all duration-300 pt-0",
          highlighted
            ? "ring-8 ring-amber-500 bg-gradient-to-b from-red-600 to-red-700/20 shadow-2xl shadow-orange-500/40 hover:shadow-orange-500/60"
            : ""
        )}
      >
        <CardHeader className="p-0 relative">
          <Image
            src={image || "/window.svg"}
            alt={title}
            width={600}
            height={200}
            className={cn(
              highlighted && "border-amber-500 border-b-8",
              "w-full h-52 object-cover duration-300 group-hover:saturate-[1.2] transition-all ease-in-out group-hover:scale-105"
            )}
          />
          <div className="absolute top-3 right-3 flex gap-2 z-20">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8 backdrop-blur-md bg-background/30"
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Like</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          {highlighted && (
            <Badge className="absolute text-base font-bold px-4 py-1 rounded-2xl top-3 left-3 bg-amber-500 text-white border-none">
              <Flame color="white" fill="white" /> HOT{" "}
              <Flame color="white" fill="white" />
            </Badge>
          )}
        </CardHeader>
        <CardContent className={"px-4 md:px-6 pt-0 pb-1"}>
          <div className="flex justify-between items-center mb-1">
            <h3 className="font-bold text-lg md:text-xl tracking-tight text-card-foreground">
              {title}
            </h3>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 rounded-full hover:bg-accent hover:text-accent-foreground"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Share</DropdownMenuItem>
                <DropdownMenuItem>Report</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="flex text-sm items-center gap-2 mb-3">
            <div
              className={cn(
                "font-semibold",
                highlighted ? "text-yellow-300" : "text-muted-foreground"
              )}
            >
              Created by
            </div>
            <div className="flex items-center gap-0.5">
              <Avatar className="size-5">
                <AvatarImage
                  src={
                    "https://xvatar.vercel.app/api/avatar/" +
                    title +
                    ".svg?rounded=40&size=80"
                  }
                  alt="Creator"
                />
                <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                  JS
                </AvatarFallback>
              </Avatar>
              <span className="font-medium">JackSparrow</span>
            </div>
          </div>

          <div className="flex text-sm justify-between items-center mb-4">
            <div
              className={cn(
                "font-semibold",
                highlighted ? "text-yellow-300" : "text-muted-foreground"
              )}
            >
              Rate per 1000 Views
            </div>
            <Badge
              variant={"outline"}
              className={"font-bold text-base border-0"}
            >
              $800
            </Badge>
          </div>

          <div
            className={cn(
              "h-px -translate-y-2 w-full",
              highlighted ? "bg-yellow-300/40" : "bg-muted-foreground/40"
            )}
          />

          <div className="flex text-sm justify-between items-center mb-6">
            <div
              className={cn(
                "font-semibold",
                highlighted ? "text-yellow-300" : "text-muted-foreground"
              )}
            >
              Accepted Platforms
            </div>
            <div className="flex gap-1.5">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Twitter className="p-1 bg-blue-500 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Twitter</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Glasses className="p-1 bg-indigo-500 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Discord</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Send className="p-1 bg-cyan-400 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Telegram</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          <div
            className={cn(
              "flex relative flex-col p-8 rounded-xl z-60 border-4 backdrop-blur-lg bg-white/5 shadow-2xl",
              highlighted
                ? "border-amber-500 shadow-black/60"
                : "border-muted-foreground/30 border-2  shadow-black/30"
            )}
          >
            <div className="flex text-white font-bold text-base justify-between items-center mb-3">
              <div>Pool</div>
              <div>$10,000</div>
            </div>

            <div className="relative mb-4">
              <Slider
                defaultValue={[60]}
                max={100}
                step={1}
                disabled
                className="mb-1"
                trackClassName={
                  highlighted ? "bg-amber-500/20" : "bg-muted-foreground/50"
                }
                rangeClassName={highlighted ? "bg-amber-500" : "bg-primary"}
                thumbClassName={
                  highlighted
                    ? "bg-red-800 border-amber-500 hover:ring-amber-500/20"
                    : "bg-primary bg-muted"
                }
              ></Slider>
              <div className="flex justify-between text-xs text-white mt-1">
                <span>0%</span>
                <span>100%</span>
              </div>
            </div>
          </div>
        </CardContent>
        {highlighted && (
          <div className="absolute w-full h-full rounded-2xl overflow-hidden">
            <Image
              src={fire1}
              alt=""
              className="absolute -bottom-0 brightness-105 group-hover:scale-110 transition-all duration-500 left-0 rounded-2xl overflow-hidden"
              width={230}
              height={100}
            />
            <Image
              src={fire1}
              alt=""
              className="absolute -bottom-0 brightness-105 group-hover:scale-110 transition-all duration-500 left-0 rounded-2xl overflow-hidden"
              width={230}
              height={100}
            />
            <Image
              src={fire2}
              alt=""
              className="absolute -bottom-0 brightness-105 group-hover:scale-110 transition-all duration-700 right-0 rounded-2xl overflow-hidden"
              width={230}
              height={100}
            />
          </div>
        )}
      </Card>
    </motion.div>
  );
}
