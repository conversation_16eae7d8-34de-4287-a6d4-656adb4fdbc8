"use client";

import { motion } from "motion/react";
import { Play, Wallet } from "lucide-react";
import Image from "next/image";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export default function CreatorDashboard() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen flex flex-col"
    >
      {/* Main Content */}
      <main className="flex-1 py-14 px-6 md:px-8 max-w-5xl mx-auto w-full">
        <Card className="w-full rounded-3xl overflow-hidden border shadow-xl p-6">
          {/* User Profile */}
          <div className="flex flex-wrap gap-10 justify-between items-center mb-8">
            <div className="flex items-start gap-4">
              <Avatar className="size-16">
                <AvatarImage
                  src="https://xvatar.vercel.app/api/avatar/ayrrad.svg?rounded=40&size=80"
                  className="object-cover"
                />
                <AvatarFallback className="text-xl bg-primary text-primary-foreground">
                  A
                </AvatarFallback>
              </Avatar>

              <div>
                <h2 className="text-xl md:text-2xl font-semibold">Ayrrad830</h2>
                <p className="text-sm  md:text-base text-muted-foreground">
                  Hello, Welcome back!
                </p>

                <div className="flex mt-1 gap-2">
                  <Button
                    size="icon"
                    className="rounded-full p-2 size-8 bg-cyan-500 hover:bg-cyan-600"
                  >
                    <Image
                      src={"/telegram.svg"}
                      alt="telegram"
                      className=""
                      width={24}
                      height={24}
                    />
                  </Button>
                  <Button
                    size="icon"
                    className="rounded-full p-2.5 bg-zinc-800 size-8 hover:bg-zinc-900"
                  >
                    <Image src={"/X.svg"} alt="x" width={24} height={24} />
                  </Button>
                  <Button
                    size="icon"
                    className="rounded-full p-1 size-8 bg-indigo-500 hover:bg-indigo-600"
                  >
                    <Image
                      src={"/discord.svg"}
                      alt="discord"
                      width={25}
                      height={25}
                    />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4 md:-translate-y-4">
              <Button className="rounded-full md:text-base font-semibold">
                Withdraw Funds
              </Button>
              <div className="flex items-center gap-2">
                <Wallet className="size-4 md:size--5 text-primary" />
                <span className="md:text-xl font-bold">$1,340.00</span>
              </div>
            </div>
          </div>

          {/* Earnings Section */}
          <div className="mb-6">
            <h3 className="text-lg md:text-2xl font-medium mb-4">Earnings</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <StatCard
                title="Lifetime earnings"
                value="$30,000.00"
                percentage="+12.94%"
              />
              <StatCard
                title="Monthly earnings"
                value="$1,000.00"
                percentage="+12.94%"
              />
              <StatCard
                title="Weekly earnings"
                value="$400.00"
                percentage="+12.94%"
              />
              <StatCard
                title="24h earnings"
                value="$400.00"
                percentage="+12.94%"
              />
            </div>
          </div>

          {/* Views Section */}
          <div className="mb-6">
            <h3 className="text-lg md:text-2xl font-medium mb-4">Views</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <StatCard
                title="Lifetime views"
                value="2.2M"
                percentage="+12.94%"
              />
              <StatCard
                title="Monthly views"
                value="900,000"
                percentage="+12.94%"
              />
              <StatCard
                title="Weekly views"
                value="300,000"
                percentage="+12.94%"
              />
              <StatCard title="24h views" value="24,000" percentage="+12.94%" />
            </div>
          </div>

          {/* Campaigns Section */}
          <div>
            <h3 className="text-lg md:text-2xl font-medium mb-4">Campaigns</h3>
            <Tabs defaultValue="active" className="w-full">
              <TabsList className="flex gap-2 bg-transparent justify-start">
                <TabsTrigger
                  value="active"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-primary border-0 rounded-none px-4 py-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  Active Campaigns
                </TabsTrigger>
                <TabsTrigger
                  value="finished"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-primary border-0 rounded-none px-4 py-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  Finished Campaigns
                </TabsTrigger>
              </TabsList>

              <TabsContent value="active" className="mt-4">
                <div className="space-y-4">
                  {[1, 2, 3].map((item) => (
                    <CampaignRow key={item} />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="finished" className="mt-4">
                <div className="text-center py-8 text-muted-foreground">
                  No finished campaigns yet
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </Card>
      </main>
    </motion.div>
  );
}

function StatCard({
  title,
  value,
  percentage,
}: {
  title: string;
  value: string;
  percentage: string;
}) {
  return (
    <Card className="overflow-hidden gap-1 bg-secondary/40 border-2 dark:border-slate-200/30 p-6 py-8 ">
      <div className="text-sm md:text-xl text-muted-foreground">{title}</div>
      <div className="flex items-center gap-4">
        <div className="text-2xl font-bold">{value}</div>
        <div className="text-sm md:text-base text-green-600 font-bold">
          {percentage}
        </div>
      </div>
    </Card>
  );
}

function CampaignRow() {
  return (
    <Card className="overflow-hidden border shadow-sm p-4">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="relative w-36 h-24 rounded-md border border-secondary overflow-hidden bg-muted">
            <div className="absolute inset-0 flex items-center justify-center">
              <Play className="size-6 text-white fill-white" />
            </div>
            <Image
              src="https://image.lexica.art/full_webp/3cbf9551-8aa9-47c7-8589-eaac886ad3b8"
              alt="Pool thumbnail"
              width={120}
              height={64}
              className="object-cover"
            />
          </div>
          <div>
            <h4 className="font-semibold tracking-tight md:text-xl">
              Afl6z Ultimate Vid
            </h4>
            <p className="text-muted-foreground inline-flex items-center gap-1">
              Created by
              <span>
                <Image
                  src="https://xvatar.vercel.app/api/avatar/ssa.svg?rounded=40&size=80"
                  alt="window"
                  width={16}
                  height={16}
                />
              </span>
              <span className="text-foreground text-lg">You</span>
            </p>
          </div>
        </div>

        <div className="flex items-center gap-6 md:gap-10">
          <div className="flex flex-col font-semibold md:text-xl">
            Views 2.2M
          </div>

          <div className="flex flex-col font-semibold md:text-xl text-primary">
            Earned $236
          </div>
        </div>
      </div>
    </Card>
  );
}
