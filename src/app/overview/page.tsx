"use client";

import Image from "next/image";
import CampaignStats from "@/components/Stats";
import CommentThread from "@/components/CommentThread";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { Play } from "lucide-react";

export default function CampaignPage() {
  return (
    <div className="min-h-screen mt-4">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 pb-8">
        <div className="md:grid md:grid-cols-3 md:gap-8">
          <div className="space-y-6 md:col-span-2">
            <div className="rounded-xl overflow-hidden">
              <Image
                src="https://image.lexica.art/full_webp/f3a87e9b-44c1-4858-9dc0-e7f523b09830"
                alt="Campaign Hero"
                width={600}
                height={300}
                className="w-full object-cover max-h-80"
              />
            </div>

            {/* Campaign Title */}
            <div>
              <h1 className="text-2xl font-bold">Ai16z Campaign</h1>
              <div className="flex items-center mt-1 text-sm text-muted-foreground">
                <span>Created by</span>
                <div className="flex items-center ml-1">
                  <div className="w-5 h-5 rounded-full bg-blue-500 mr-1"></div>
                  <span>JackSparow</span>
                </div>
              </div>
            </div>

            {/* Tabs */}
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full mb-4 grid-cols-2 gap-2 max-w-[300px]">
                <TabsTrigger
                  value="overview"
                  className="bg-foreground text-base dark:text-background text-background data-[state=active]:text-black dark:data-[state=active]:text-black rounded-full data-[state=active]:bg-primary dark:data-[state=active]:bg-primary"
                >
                  Overview
                </TabsTrigger>
                <TabsTrigger
                  value="leaderboard"
                  className="bg-foreground text-base dark:text-background text-background data-[state=active]:text-black dark:data-[state=active]:text-black rounded-full data-[state=active]:bg-primary dark:data-[state=active]:bg-primary"
                >
                  Leaderboard
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview">
                {/* Description */}
                <div>
                  <h2 className="text-lg font-medium mb-2">Description</h2>
                  <p className="text-muted-foreground">
                    Lorem ipsum dolor sit amet consectetur. Risus sagittis nibh
                    ornare consequat. Et suspendisse lectus iaculis mauris
                    natoque justo faucibus. Sem nascetur nisi dolor eget quisque
                    venenatis sollicitudin ac convallis. In vitae lectus
                    suspendisse morbi varius purus auctor amet. Neque libero
                    nulla blandit odio.
                  </p>
                </div>

                {/* Thread */}
                <CommentThread />
              </TabsContent>

              <TabsContent value="leaderboard">
                {/* Leaderboard */}
                <div>
                  <div className="space-y-2">
                    {[1, 2, 3, 4, 5].map((item) => (
                      <LeaderboardRow key={item} rank={item} />
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Right Column - Campaign Stats */}
          <div className="mt-8 md:mt-0">
            <CampaignStats />
          </div>
        </div>
      </div>
    </div>
  );
}

function LeaderboardRow({ rank }: { rank: number }) {
  const poolData = [
    { name: "Afl6z Ultimate Vid", views: "2.2M", earned: "236" },
    { name: "Crypto Mastery", views: "1.8M", earned: "186" },
    { name: "NFT Collection", views: "1.2M", earned: "124" },
    { name: "Blockchain Basics", views: "980K", earned: "98" },
    { name: "Meta Universe", views: "760K", earned: "76" },
  ];

  const pool = poolData[rank - 1];

  return (
    <Card className="overflow-hidden border shadow-sm p-4 scale-95">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="relative w-36 h-24 rounded-md border border-secondary overflow-hidden bg-muted">
            <div className="absolute inset-0 flex items-center justify-center">
              <Play className="size-6 text-white fill-white" />
            </div>
            <Image
              src="https://image.lexica.art/full_webp/3cbf9551-8aa9-47c7-8589-eaac886ad3b8"
              alt="Pool thumbnail"
              width={120}
              height={64}
              className="object-cover"
            />
          </div>
          <div>
            <h4 className="font-semibold tracking-tight md:text-xl">
              {pool.name}
            </h4>
            <p className="text-muted-foreground inline-flex items-center gap-1">
              Created by
              <span>
                <Image
                  src="https://xvatar.vercel.app/api/avatar/ssa.svg?rounded=40&size=80"
                  alt="window"
                  width={16}
                  height={16}
                />
              </span>
              <span className="text-foreground text-lg">You</span>
            </p>
          </div>
        </div>

        <div className="flex items-center gap-6 md:gap-10">
          <div className="flex flex-col font-semibold md:text-xl">
            Views {pool.views}
          </div>

          <div className="flex flex-col font-semibold md:text-xl text-primary">
            Earned ${pool.earned}
          </div>
        </div>
      </div>
    </Card>
  );
}
