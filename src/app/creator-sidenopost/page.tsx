"use client";

import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import sidehand from "@/assets/sidehand.png";
import finger from "@/assets/fingers.png";
import { useState } from "react";

import { Clock, ArrowRight } from "lucide-react";
import Image from "next/image";

export default function CampaignAnnouncementsPage() {
  const [activeTab, setActiveTab] = useState("announcements");

  return (
    <div className="min-h-screen bg-background">
      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* Left Content - Campaign Details */}
          <div className="lg:col-span-2">
            <Card className="overflow-hidden">
              <CardContent className="p-0 mx-4">
                {/* Campaign Header */}

                {/* Campaign Image */}
                <div className="relative aspect-video p-4 overflow-hidden rounded-lg">
                  <div className="p-2 relative z-20">
                    <div className="flex items-center justify-between mb-1">
                      <h1 className="text-2xl font-bold text-white">
                        Joyner Lucas &quot;White Noise&quot; Campaign
                      </h1>
                    </div>
                    <div className="flex items-center text-sm text-white">
                      <Clock className="w-4 h-4 mr-1 text-primary" />
                      <span>Ends 15/14/2026 10:59 PM</span>
                    </div>
                  </div>
                  <Image
                    src="https://image.lexica.art/full_webp/f3a87e9b-44c1-4858-9dc0-e7f523b09830"
                    alt="Joyner Lucas Campaign"
                    width={600}
                    height={300}
                    className="object-cover absolute inset-0 size-full"
                  />
                  <div className="absolute w-full h-1/2 top-0 bg-gradient-to-b from-black/80 to-transparent" />
                </div>

                {/* Created By */}
                <div className="mt-4 mb-8">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <span>Created by</span>
                    <div className="flex items-center ml-2">
                      <div className="w-4 h-4 bg-primary rounded-sm mr-1"></div>
                      <span className="text-foreground font-medium">
                        PromoteFun
                      </span>
                    </div>
                  </div>
                </div>

                {/* Tabs */}
                <div>
                  <div className="flex gap-1 sm:px-6">
                    <button
                      onClick={() => setActiveTab("overview")}
                      className={`px-6 py-2 text-sm font-medium rounded-full ${
                        activeTab === "overview"
                          ? "bg-primary text-primary-foreground"
                          : "bg-white text-black"
                      }`}
                    >
                      Overview
                    </button>
                    <button
                      onClick={() => setActiveTab("leaderboard")}
                      className={`px-6 py-2 text-sm font-medium rounded-full ${
                        activeTab === "leaderboard"
                          ? "bg-primary text-primary-foreground"
                          : "bg-white text-black"
                      }`}
                    >
                      Leaderboard
                    </button>
                    <button
                      onClick={() => setActiveTab("announcements")}
                      className={`px-6 py-2 text-sm font-medium rounded-full ${
                        activeTab === "announcements"
                          ? "bg-primary text-primary-foreground"
                          : "bg-white text-black"
                      }`}
                    >
                      Announcements
                    </button>
                  </div>
                </div>

                {/* Tab Content */}
                {activeTab === "overview" && (
                  <div className="p-6 space-y-6">
                    {/* Description */}
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-3">
                        Description
                      </h3>
                      <div className="text-sm text-muted-foreground space-y-3">
                        <p>
                          The internet&apos;s most iconic meme is going viral
                          again and you&apos;re invited to cash in. The
                          &quot;GIGACHAD&quot; Clipping Campaign&apos;s here to
                          spread the word of the ultimate &quot;alpha&quot; meme
                          coin, $GIGA, through short- form content on TikTok and
                          Instagram.
                        </p>
                        <p>
                          You&apos;ll get access to a library of GIGACHAD clips,
                          sounds, and templates to help you create fun,
                          eye-catching GIGACHAD memes.
                        </p>
                        <p>
                          Submit original short-form clips to this campaign and
                          earn $1,000 for every 1 million views. Upload directly
                          from your TikTok or Instagram account, track your
                          stats here Promote.fun, and get rewarded as your
                          content spreads.
                        </p>
                      </div>
                    </div>

                    {/* Content Requirements */}
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-3">
                        Content Requirements
                      </h3>
                      <div className="text-sm text-muted-foreground space-y-3">
                        <p>
                          Videos must be Gigachad-related memes. These are short
                          videos that use the &quot;GIGACHAD&quot; character or
                          theme to show confidence, boldness, or over-the-top
                          &quot;alpha&quot; behavior, often in a funny or
                          exaggerated way.
                        </p>
                        <p>
                          You can use the provided sounds, memes, and formats as
                          content or templates, but you&apos;re free to create
                          your own take as long as it clearly fits the GIGACHAD
                          vibe. The content library includes hundreds of
                          GIGACHAD meme videos, GIGACHAD audios, images and
                          watermarks.
                        </p>
                        <p className="font-medium text-foreground">
                          To qualify for payouts, videos must:
                        </p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>Show the GIGA token visually in the video</li>
                          <li>Use the provided watermarks</li>
                        </ul>
                        <p>
                          Only original videos that follow these rules will be
                          accepted.
                        </p>
                      </div>
                    </div>

                    {/* Content Library Button */}
                    <Button className="bg-primary text-primary-foreground hover:bg-primary/90">
                      Content library
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                )}

                {activeTab === "leaderboard" && (
                  <div className="p-6">
                    <h3 className="text-lg font-semibold text-foreground mb-4">
                      Leaderboard
                    </h3>
                    <p className="text-muted-foreground">
                      Leaderboard content coming soon...
                    </p>
                  </div>
                )}

                {activeTab === "announcements" && (
                  <div className="p-6 space-y-2 text-muted-foreground text-center border-2 mt-2 rounded-lg">
                    User has not posted any announcements yet.
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Sidebar - Campaign Overview */}
          <div className="lg:col-span-1">
            <Card className="relative">
              <Image
                src={sidehand}
                alt=""
                width={100}
                height={200}
                className="absolute top-2 -right-3"
              />
              <Image
                src={finger}
                alt=""
                width={60}
                height={200}
                className="absolute -top-10 left-32"
              />
              <CardContent className="p-4">
                {/* Overview Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center w-full">
                    <h2 className="text-xl font-semibold text-foreground text-center w-full">
                      Overview
                    </h2>
                  </div>
                </div>

                {/* Pool Progress */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">Pool</span>
                    <span className="text-2xl font-bold text-primary">
                      $10,000
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2 mb-2">
                    <div className="bg-primary h-2 rounded-full w-[53%]"></div>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-xs text-primary">$500 paid out so far</p>
                    <p className="text-xs text-primary">$9500 remaining</p>
                  </div>
                </div>

                {/* Campaign Details */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between py-2 border-2 border-primary bg-primary/10 rounded-full px-4">
                    <span className="text-sm text-foreground">
                      Total Budget
                    </span>
                    <span className="text-lg font-semibold text-primary">
                      $10,000
                    </span>
                  </div>
                  <div className="flex items-center justify-between py-2 border-2 border-gray-700 rounded-full px-4">
                    <span className="text-sm text-muted-foreground">Title</span>
                    <span className="text-lg text-foreground">
                      Joyner Lucas
                    </span>
                  </div>
                  <div className="flex items-center justify-between py-2 border-2 border-gray-700 rounded-full px-4">
                    <span className="text-sm text-muted-foreground">
                      Rate per Hour
                    </span>
                    <span className="text-lg font-semibold text-foreground">
                      $64
                    </span>
                  </div>
                  <div className="flex items-center justify-between py-2 border-2 border-gray-700 rounded-full px-4">
                    <span className="text-sm text-muted-foreground">
                      Accepted platforms
                    </span>
                    <div className="flex items-center space-x-2">
                      {/* Twitch Icon */}
                      <div className="w-7 h-7 bg-purple-600 rounded-full flex items-center justify-center">
                        <svg viewBox="0 0 24 24" className="w-4 h-4 fill-white">
                          <path d="M11.571 4.714h1.715v5.143H11.57zm4.715 0H18v5.143h-1.714zM6 0L1.714 4.286v15.428h5.143V24l4.286-4.286h3.428L22.286 12V0zm14.571 11.143l-3.428 3.428h-3.429l-3 3v-3H6.857V1.714h13.714Z" />
                        </svg>
                      </div>
                      {/* YouTube Icon */}
                      <div className="w-7 h-7 bg-red-600 rounded-full flex items-center justify-center">
                        <svg viewBox="0 0 24 24" className="w-4 h-4 fill-white">
                          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-2">
                  <Button className="w-full rounded-full bg-primary text-primary-foreground hover:bg-primary/90">
                    Join Campaign
                  </Button>
                  <Button
                    variant="secondary"
                    className="w-full rounded-full bg-secondary/30 text-muted-foreground"
                  >
                    Edit campaign
                  </Button>
                  <Button
                    variant="secondary"
                    className="w-full rounded-full bg-secondary/30 text-muted-foreground"
                  >
                    Campaign dashboard
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
