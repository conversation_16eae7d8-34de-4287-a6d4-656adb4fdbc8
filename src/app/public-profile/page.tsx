"use client";

import { motion } from "motion/react";
import { Play } from "lucide-react";
import Image from "next/image";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export default function PublicProfile() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen flex flex-col"
    >
      {/* Main Content */}
      <main className="flex-1 p-8 max-w-5xl mx-auto w-full">
        <Card className="w-full rounded-3xl overflow-hidden border shadow-xl pt-0">
          {/* Banner Image */}
          <div className="relative w-full h-48">
            <Image
              src="https://image.lexica.art/full_webp/705b1019-8dcc-4848-9d5c-1e1779a0cb71"
              alt="Profile banner"
              width={1200}
              height={180}
              className="w-full h-full object-cover opacity-90"
            />
          </div>

          {/* Profile Section */}
          <CardContent className="pt-0 pb-6 -mt-24 flex flex-col items-center relative">
            {/* Avatar */}
            <Avatar className="size-36 border-4 border-card">
              <AvatarImage
                src="https://image.lexica.art/full_webp/705b1019-8dcc-4848-9d5c-1e1779a0cb71"
                className="object-cover"
              />
              <AvatarFallback className="text-xl bg-primary text-primary-foreground">
                A
              </AvatarFallback>
            </Avatar>

            <h2 className="text-xl md:text-2xl mt-2 font-semibold">
              Anime Girl
            </h2>
            <p className="text-sm text-foreground/70 mb-4">
              <span className="text-foreground text-base mx-2">223k</span>
              Followers
            </p>

            <Button
              size={"lg"}
              className="rounded-full absolute top-24 text-base font-bold right-6"
            >
              Follow
            </Button>

            {/* Social Icons */}
            <div className="flex absolute top-24 left-6 justify-center gap-3">
              <Button
                size="icon"
                className="bg-cyan-500 hover:bg-cyan-600 rounded-full p-2"
              >
                <Image
                  src="/telegram.svg"
                  alt="telegram"
                  width={24}
                  height={24}
                />
              </Button>
              <Button
                size="icon"
                className="bg-zinc-800 hover:bg-zinc-900 rounded-full p-2"
              >
                <Image src="/X.svg" alt="x" width={24} height={24} />
              </Button>
              <Button
                size="icon"
                className="bg-indigo-500 hover:bg-indigo-600 rounded-full p-2"
              >
                <Image
                  src="/discord.svg"
                  alt="discord"
                  width={24}
                  height={24}
                />
              </Button>
            </div>
          </CardContent>

          <div className="md:px-8 px-4 pb-6 mt-2">
            <div className="mb-8">
              <h3 className="text-lg md:text-2xl font-medium mb-4">Earnings</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <StatCard
                  title="Lifetime earnings"
                  value="$30,000.00"
                  percentage="+12.94%"
                />
                <StatCard
                  title="Monthly earnings"
                  value="$1,000.00"
                  percentage="+12.94%"
                />
                <StatCard
                  title="Weekly earnings"
                  value="$400.00"
                  percentage="+12.94%"
                />
                <StatCard
                  title="24h earnings"
                  value="$400.00"
                  percentage="+12.94%"
                />
              </div>
            </div>

            {/* Views Section */}
            <div className="mb-8">
              <h3 className="text-lg md:text-2xl font-medium mb-4">Views</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <StatCard
                  title="Lifetime views"
                  value="2.2M"
                  percentage="+12.94%"
                />
                <StatCard
                  title="Monthly views"
                  value="900,000"
                  percentage="+12.94%"
                />
                <StatCard
                  title="Weekly views"
                  value="300,000"
                  percentage="+12.94%"
                />
                <StatCard
                  title="24h views"
                  value="24,000"
                  percentage="+12.94%"
                />
              </div>
            </div>

            {/* Pools Section */}
            <div>
              <h3 className="text-lg font-medium mb-4">Pools</h3>
              <div className="space-y-4">
                {[1, 2, 3].map((item) => (
                  <PoolRow key={item} />
                ))}
              </div>
            </div>
          </div>
        </Card>
      </main>
    </motion.div>
  );
}

function StatCard({
  title,
  value,
  percentage,
}: {
  title: string;
  value: string;
  percentage: string;
}) {
  return (
    <Card className="overflow-hidden gap-1 bg-secondary/40 border-2 dark:border-slate-200/30 p-6 py-8 ">
      <div className="text-sm md:text-xl text-muted-foreground">{title}</div>
      <div className="flex items-center gap-4">
        <div className="text-2xl font-bold">{value}</div>
        <div className="text-sm md:text-base text-green-600 font-bold">
          {percentage}
        </div>
      </div>
    </Card>
  );
}

function PoolRow() {
  return (
    <Card className="overflow-hidden border shadow-sm p-4">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="relative w-36 h-24 rounded-md border border-secondary overflow-hidden bg-muted">
            <div className="absolute inset-0 flex items-center justify-center">
              <Play className="size-6 text-white fill-white" />
            </div>
            <Image
              src="https://image.lexica.art/full_webp/3cbf9551-8aa9-47c7-8589-eaac886ad3b8"
              alt="Pool thumbnail"
              width={120}
              height={64}
              className="object-cover"
            />
          </div>
          <div>
            <h4 className="font-semibold tracking-tight md:text-xl">
              Afl6z Ultimate Vid
            </h4>
            <p className="text-muted-foreground inline-flex items-center gap-1">
              Created by
              <span>
                <Image
                  src="https://xvatar.vercel.app/api/avatar/ssa.svg?rounded=40&size=80"
                  alt="window"
                  width={16}
                  height={16}
                />
              </span>
              <span className="text-foreground text-lg">You</span>
            </p>
          </div>
        </div>

        <div className="flex items-center gap-6 md:gap-10">
          <div className="flex flex-col font-semibold md:text-xl">
            Views 2.2M
          </div>

          <div className="flex flex-col font-semibold md:text-xl text-primary">
            Earned $236
          </div>
        </div>
      </div>
    </Card>
  );
}
