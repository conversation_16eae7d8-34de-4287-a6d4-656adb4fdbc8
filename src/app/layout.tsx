import type { <PERSON>ada<PERSON> } from "next";
import { Fustat } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import ToasterwithTheme from "@/components/ui/ToasterWithTheme";
import Header from "@/components/sections/Header";
import Footer from "@/components/sections/Footer";

// const geistSans = Geist({
//   variable: "--font-geist-sans",
//   subsets: ["latin"],
// });

// const geistMono = Geist_Mono({
//   variable: "--font-geist-mono",
//   subsets: ["latin"],
// });

const fustat = Fustat({
  variable: "--font-fustat",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "PromoteFun",
  description: "PromoteFun - The best way to promote your business",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html suppressHydrationWarning lang="en">
      <body className={`${fustat.className} antialiased`}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
          <Header />
          {children}
          <Footer />
          <ToasterwithTheme />
        </ThemeProvider>
      </body>
    </html>
  );
}
