"use client";

import { motion } from "motion/react";
import {
  Plus,
  Search,
  Filter,
  ArrowUpDown,
  Folder,
  Calendar,
  DollarSign,
  Download,
  Eye,
  BarChart3,
  type LucideIcon,
} from "lucide-react";
import Image from "next/image";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

// Mock data
const stats = [
  {
    title: "Total Campaigns",
    value: "3",
    icon: Folder,
  },
  {
    title: "Client Since",
    value: "JUL 17, 2025",
    icon: Calendar,
  },
  {
    title: "Total Spent",
    value: "$10,000",
    icon: DollarSign,
  },
  {
    title: "Total Posts",
    value: "110",
    icon: Download,
  },
  {
    title: "Total Views",
    value: "747,849,049",
    icon: Eye,
  },
  {
    title: "Average CPM",
    value: "$0.07",
    icon: BarChart3,
  },
];

const campaigns = [
  {
    id: "1",
    title: '<PERSON><PERSON> "LIAR LIAR" Audio Campaign',
    image:
      "https://image.lexica.art/full_webp/3cbf9551-8aa9-47c7-8589-eaac886ad3b8",
    totalBudget: "$10K",
    rate: "$0.8/M",
    totalViews: "45.9K",
    submissions: 435,
    poolAmount: "$1532",
    progress: 75,
  },
  {
    id: "2",
    title: "Giga Campaign",
    image:
      "https://image.lexica.art/full_webp/3cbf9551-8aa9-47c7-8589-eaac886ad3b8",
    totalBudget: "$10K",
    rate: "$0.8/M",
    totalViews: "45.9K",
    submissions: 435,
    poolAmount: "$1532",
    progress: 75,
  },
  {
    id: "3",
    title: 'Ainsley "LIAR LIAR" Audio Campaign',
    image:
      "https://image.lexica.art/full_webp/3cbf9551-8aa9-47c7-8589-eaac886ad3b8",
    totalBudget: "$10K",
    rate: "$0.8/M",
    totalViews: "45.9K",
    submissions: 435,
    poolAmount: "$1532",
    progress: 75,
  },
  {
    id: "4",
    title: "Giga Campaign",
    image:
      "https://image.lexica.art/full_webp/3cbf9551-8aa9-47c7-8589-eaac886ad3b8",
    totalBudget: "$10K",
    rate: "$0.8/M",
    totalViews: "45.9K",
    submissions: 435,
    poolAmount: "$1532",
    progress: 75,
  },
];

function StatCard({
  title,
  value,
  icon: Icon,
}: {
  title: string;
  value: string;
  icon: LucideIcon;
}) {
  return (
    <Card className="bg-card border-border p-6 rounded-2xl">
      <div className="flex items-center gap-4">
        <div className="p-3 bg-primary/20 rounded-xl">
          <Icon className="w-6 h-6 text-primary" />
        </div>
        <div>
          <p className="text-muted-foreground text-sm font-medium">{title}</p>
          <p className="text-foreground text-xl font-bold">{value}</p>
        </div>
      </div>
    </Card>
  );
}

function CampaignCard({ campaign }: { campaign: (typeof campaigns)[0] }) {
  return (
    <Card className="bg-card border-border p-4 rounded-2xl hover:border-primary">
      <div className="flex gap-6 md:gap-8">
        {/* Campaign Image */}
        <div className="w-40 h-40 rounded-lg overflow-hidden bg-muted flex-shrink-0">
          <Image
            src={campaign.image}
            alt={campaign.title}
            width={128}
            height={96}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Campaign Content */}
        <div className="flex-1">
          <h3 className="text-foreground text-xl font-bold mb-4">
            {campaign.title}
          </h3>

          {/* Metrics Row */}
          <div className="flex items-center gap-8 mb-4">
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4 text-primary" />
              <div>
                <p className="text-muted-foreground text-sm">Total Budget</p>
                <p className="text-foreground font-semibold">
                  {campaign.totalBudget}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4 text-primary" />
              <div>
                <p className="text-muted-foreground text-sm">Rate</p>
                <p className="text-foreground font-semibold">{campaign.rate}</p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Eye className="w-4 h-4 text-primary" />
              <div>
                <p className="text-muted-foreground text-sm">Total Views</p>
                <p className="text-foreground font-semibold">
                  {campaign.totalViews}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Download className="w-4 h-4 text-primary" />
              <div>
                <p className="text-muted-foreground text-sm">Submissions</p>
                <p className="text-foreground font-semibold">
                  {campaign.submissions}
                </p>
              </div>
            </div>
          </div>

          {/* Progress Bar and Pool */}
          <div className="flex items-center justify-between">
            <div className="flex-1 mr-4">
              <p className="text-muted-foreground text-sm mb-2">Pool</p>
              <div className="w-full bg-gray-700/70 rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full"
                  style={{ width: `${campaign.progress}%` }}
                ></div>
              </div>
            </div>
            <div className="text-primary text-xl font-bold">
              {campaign.poolAmount}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}

export default function ActiveCampaignsPage() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-background p-6"
    >
      <div className="max-w-5xl mx-auto pt-12 mb-40">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold text-foreground">
              Active Campaigns
            </h1>
            <Badge className="bg-primary text-primary-foreground text-lg font-bold rounded-full">
              748
            </Badge>
          </div>
          <Button className="bg-primary hover:bg-primary/90 text-primary-foreground font-semibold rounded-full px-6 py-3">
            <Plus className="w-5 h-5 mr-2" />
            Create New Campaign
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
            <Input
              placeholder="Search..."
              className="pl-12 h-12 bg-card border-border rounded-full text-foreground"
            />
          </div>
          <Button
            variant="outline"
            className="h-12 px-6 rounded-full bg-card border-border text-foreground"
          >
            <Filter className="w-5 h-5 mr-2" />
            Filters
          </Button>
          <Button
            variant="outline"
            className="h-12 px-6 rounded-full bg-card border-border text-foreground"
          >
            <ArrowUpDown className="w-5 h-5 mr-2" />
            Sort by
          </Button>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {stats.map((stat, index) => (
            <StatCard key={index} {...stat} />
          ))}
        </div>

        {/* Campaign Cards */}
        <div className="space-y-5">
          {campaigns.map((campaign) => (
            <CampaignCard key={campaign.id} campaign={campaign} />
          ))}
        </div>
      </div>
    </motion.div>
  );
}
